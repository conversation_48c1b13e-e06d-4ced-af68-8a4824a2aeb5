import { JsonValue, PlainMessage } from '@bufbuild/protobuf';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

import { usePlatformApiFetch } from '@ui/feature/shared/hooks/use-platform-api-fetch';
import { usePlatformApiWatch } from '@ui/feature/shared/hooks/use-platform-api-watch';
import { useKubeVisionContext } from '@ui/lib/context/kubevision-context';
import { Subset, UseQueryOptionsOverride } from '@ui/lib/types';

import { GetInstanceResponse, InstanceSpec, ListInstancesResponse } from '../argocd/v1/argocd_pb';
import { queryKeys } from '../query-keys';
import { getQueryParamsAsString } from '../utils';

import {
  ListKubernetesEnabledClustersResponse,
  ListKubernetesNamespacesRequest,
  ListKubernetesNamespacesResponse,
  ListKubernetesResourceTypesResponse,
  ListKubernetesResourcesRequest,
  ListKubernetesResourcesResponse,
  GetKubernetesResourceDetailRequest,
  GetKubernetesResourceDetailResponse,
  ListKubernetesImagesRequest,
  ListKubernetesImagesResponse,
  ListKubernetesContainersRequest,
  ListKubernetesContainersResponse,
  GetKubernetesContainerRequest,
  GetKubernetesContainerResponse,
  GetKubernetesManifestRequest,
  GetKubernetesManifestResponse,
  GetKubernetesLogsRequest,
  GetKubernetesLogsResponse,
  GetKubernetesAssistantSuggestionRequest,
  GetKubernetesAssistantSuggestionResponse,
  ResolveKubernetesAssistantConversationRequest,
  ResolveKubernetesAssistantConversationResponse,
  ListKubernetesNodesRequest,
  ListKubernetesNodesResponse,
  GetKubernetesNodeRequest,
  GetKubernetesNodeResponse,
  KubernetesNode,
  GetKubernetesEventsRequest,
  GetKubernetesEventsResponse,
  ListKubernetesAuditLogsRequest,
  ListKubernetesAuditLogsResponse,
  ListKubernetesDeprecatedAPIsRequest,
  ListKubernetesDeprecatedAPIsResponse,
  ListKubernetesEnabledClustersRequest,
  ListKubernetesTimelineEventsRequest,
  ListKubernetesTimelineEventsResponse,
  ListKubernetesTimelineResourcesRequest,
  ListKubernetesTimelineResourcesResponse,
  GetKubernetesPodRequest,
  KubernetesPodDetail,
  ListKubernetesPodsRequest,
  ListKubernetesPodsResponse,
  GetKubernetesPodResponse,
  GetKubernetesImageDetailRequest,
  GetKubernetesImageDetailResponse,
  EnabledCluster,
  ListKubernetesNamespacesDetailsRequest,
  ListKubernetesNamespacesDetailsResponse,
  GetKubernetesNamespaceDetailRequest,
  GetKubernetesNamespaceDetailResponse,
  NamespaceDetail,
  GetKubernetesClusterDetailRequest,
  GetKubernetesClusterDetailResponse,
  GetKubernetesSummaryResponse,
  ListAIConversationsResponse,
  AIConversation,
  GetAIConversationResponse,
  UpdateAIConversationRequest,
  ListAIConversationsRequest,
  DeleteKubernetesResourceRequest
} from './v1/organization_pb';

const refetchConfig = {
  refetchInterval: 60000,
  refetchOnMount: true,
  refetchOnWindowFocus: true,
  refetchOnReconnect: true,
  keepPreviousData: true
};

export const useGetKubernetesResourceTypesQuery = (
  instanceId: string,
  opts?: UseQueryOptionsOverride<ListKubernetesResourceTypesResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  return useQuery({
    queryKey: queryKeys.kubevision.types(organizationId, instanceId).queryKey,
    queryFn: () =>
      apiFetch(
        `orgs/${organizationId}/k8s/resource-types?${new URLSearchParams({ instanceId })}`
      ).then(ListKubernetesResourceTypesResponse.fromJson),
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesResourceListQuery = (
  data: Omit<PlainMessage<ListKubernetesResourcesRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesResourcesResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);
  return useQuery({
    queryKey: queryKeys.kubevision.list(organizationId, 'resources', params).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/resources?${params}`).then(
        ListKubernetesResourcesResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const useDeleteKubernetesResourceMutation = () => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  return useMutation({
    mutationFn: (data: Omit<PlainMessage<DeleteKubernetesResourceRequest>, 'organizationId'>) => {
      const { instanceId, clusterId, resourceId } = data;

      return apiFetch(
        `orgs/${organizationId}/k8s/instances/${instanceId}/clusters/${clusterId}/resources/${resourceId}`,
        {
          method: 'DELETE'
        }
      );
    }
  });
};

export const useGetKubernetesEnabledClustersQuery = (
  data: Omit<
    PlainMessage<ListKubernetesEnabledClustersRequest> & { includeDisabled?: boolean },
    'organizationId'
  >,
  opts?: UseQueryOptionsOverride<EnabledCluster[], readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.clusters(organizationId, params).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/clusters?${params}`)
        .then(ListKubernetesEnabledClustersResponse.fromJson)
        .then((res) => res.clusters.filter((c) => data.includeDisabled || c.isEnabled));
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesNamespacesQuery = (
  data: Omit<PlainMessage<ListKubernetesNamespacesRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<string[], readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);
  return useQuery({
    queryKey: queryKeys.kubevision.namespaces(organizationId, params).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/namespaces?${params}`)
        .then(ListKubernetesNamespacesResponse.fromJson)
        .then((res) => res.namespaces);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesResourceDetailQuery = (
  data: Omit<PlainMessage<GetKubernetesResourceDetailRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<GetKubernetesResourceDetailResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { resourceId, ...restData } = data;
  const params = getQueryParamsAsString(restData);
  const queryKeyParams = getQueryParamsAsString(data);
  return useQuery({
    queryKey: queryKeys.kubevision.resourceDetail(organizationId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/resources/${resourceId}/detail?${params}`).then(
        GetKubernetesResourceDetailResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesManifestQuery = (
  shard: string,
  data: Omit<PlainMessage<GetKubernetesManifestRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<GetKubernetesManifestResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { resourceId, ...restData } = data;
  const params = getQueryParamsAsString(restData);
  const queryKeyParams = getQueryParamsAsString(data);
  return useQuery({
    queryKey: queryKeys.kubevision.manifest(organizationId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(
        `orgs/${organizationId}/k8s/resources/${resourceId}/manifest?${params}`,
        null,
        { shard }
      ).then(GetKubernetesManifestResponse.fromJson);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesResourceEventsQuery = (
  shard: string,
  data: Omit<PlainMessage<GetKubernetesEventsRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<GetKubernetesEventsResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { resourceId, ...restData } = data;
  const params = getQueryParamsAsString(restData);
  const queryKeyParams = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.events(organizationId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/resources/${resourceId}/events?${params}`, null, {
        shard
      }).then(GetKubernetesEventsResponse.fromJson);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useListKubernetesResourceAuditLogsQuery = (
  data: Omit<PlainMessage<ListKubernetesAuditLogsRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesAuditLogsResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { resourceId, ...restData } = data;
  const params = getQueryParamsAsString(restData);
  const queryKeyParams = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.auditLogs(organizationId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(
        `orgs/${organizationId}/k8s/resources/${resourceId}/audit-logs?${params}`
      ).then(ListKubernetesAuditLogsResponse.fromJson);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesImagesQuery = (
  data: Omit<PlainMessage<ListKubernetesImagesRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesImagesResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);
  return useQuery({
    queryKey: queryKeys.kubevision.list(organizationId, 'images', params).queryKey,
    queryFn: async () => {
      const result = await apiFetch(`orgs/${organizationId}/k8s/images?${params}`);
      return ListKubernetesImagesResponse.fromJson(result);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesImageDetailQuery = (
  data: Omit<PlainMessage<GetKubernetesImageDetailRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<GetKubernetesImageDetailResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);
  return useQuery({
    queryKey: queryKeys.kubevision.image(organizationId, params).queryKey,
    queryFn: async () => {
      const result = await apiFetch(`orgs/${organizationId}/k8s/images/detail?${params}`);
      return GetKubernetesImageDetailResponse.fromJson(result);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesContainersQuery = (
  data: Omit<PlainMessage<ListKubernetesContainersRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesContainersResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);
  return useQuery({
    queryKey: queryKeys.kubevision.list(organizationId, 'containers', params).queryKey,
    queryFn: async () => {
      const result = await apiFetch(`orgs/${organizationId}/k8s/containers?${params}`);
      return ListKubernetesContainersResponse.fromJson(result);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesContainerQuery = (
  data: Omit<PlainMessage<GetKubernetesContainerRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<GetKubernetesContainerResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { containerId, ...restData } = data;
  const params = getQueryParamsAsString(restData);
  const queryKeyParams = getQueryParamsAsString(data);
  return useQuery({
    queryKey: queryKeys.kubevision.container(organizationId, queryKeyParams).queryKey,
    queryFn: async () => {
      const result = await apiFetch(
        `orgs/${organizationId}/k8s/containers/${containerId}?${params}`
      );
      return GetKubernetesContainerResponse.fromJson(result);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesLogsQuery = (
  shard: string,
  data: Omit<PlainMessage<GetKubernetesLogsRequest>, 'organizationId'>
) => {
  const { organizationId } = useKubeVisionContext();
  const queryClient = useQueryClient();
  const [isWatchEnabled, setIsWatchEnabled] = useState(true);

  const { resourceId, ...rest } = data;
  const params = getQueryParamsAsString(rest);
  const resource = `stream/orgs/${organizationId}/k8s/resources/${resourceId}/logs?${params}`;

  const handleMessage = (value: JsonValue) => {
    const newData = GetKubernetesLogsResponse.fromJson(value);
    queryClient.setQueryData(
      queryKeys.kubevision.podLogs(organizationId, resourceId, params).queryKey,
      (oldData: GetKubernetesLogsResponse[] = []) => [...oldData, newData]
    );
    if (newData.last) {
      setIsWatchEnabled(false);
    }
  };

  useEffect(() => {
    setIsWatchEnabled(true);
  }, [params, resourceId]);

  usePlatformApiWatch({
    resource,
    onMessage: handleMessage,
    enabled: isWatchEnabled,
    shard
  });

  return useQuery({
    queryKey: queryKeys.kubevision.podLogs(organizationId, resourceId, params).queryKey,
    queryFn: () => Promise.resolve([]),
    staleTime: 500
  });
};

export const useGetKubernetesAssistantSuggestionQuery = (
  reqId: number,
  shard: string,
  data: Omit<PlainMessage<GetKubernetesAssistantSuggestionRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<GetKubernetesAssistantSuggestionResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { resourceId, ...requestPayload } = data;
  return useQuery({
    queryKey: queryKeys.kubevision.assistantSuggestion(organizationId, resourceId, reqId).queryKey,
    queryFn: async () => {
      const result = await apiFetch(
        `orgs/${organizationId}/k8s/resources/${resourceId}/assistant-suggestion`,
        {
          method: 'POST',
          body: JSON.stringify(requestPayload)
        },
        {
          shard
        }
      );
      return GetKubernetesAssistantSuggestionResponse.fromJson(result);
    },
    ...opts
  });
};

export const useResolveAssistantConversationMutation = (
  preload: Omit<
    PlainMessage<ResolveKubernetesAssistantConversationRequest>,
    'organizationId' | 'resolved' | 'state'
  >
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { resourceId, ...requestPayload } = preload;
  return useMutation({
    mutationFn: async (
      data: Pick<PlainMessage<ResolveKubernetesAssistantConversationRequest>, 'resolved' | 'state'>
    ) => {
      const result = await apiFetch(
        `orgs/${organizationId}/k8s/resources/${resourceId}/resolve-assistant-conversation`,
        {
          method: 'POST',
          body: JSON.stringify({
            ...requestPayload,
            ...data
          })
        }
      );
      return ResolveKubernetesAssistantConversationResponse.fromJson(result);
    }
  });
};

export const useGetKubernetesNodesQuery = (
  data: Omit<PlainMessage<ListKubernetesNodesRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesNodesResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);
  const queryKeyParams = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.nodes(organizationId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/nodes?${params}`).then(
        ListKubernetesNodesResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesPodsQuery = (
  data: Omit<PlainMessage<ListKubernetesPodsRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesPodsResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);
  const queryKeyParams = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.pods(organizationId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/pods?${params}`).then(
        ListKubernetesPodsResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const useListKubernetesNamespacesDetailQuery = (
  data: Omit<PlainMessage<ListKubernetesNamespacesDetailsRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesNamespacesDetailsResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);
  const queryKeyParams = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.namespacesDetails(organizationId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/namespaces-details?${params}`).then(
        ListKubernetesNamespacesDetailsResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesNamespaceDetailQuery = (
  data: Omit<PlainMessage<GetKubernetesNamespaceDetailRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<NamespaceDetail, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { namespaceId, ...rest } = data;
  const params = getQueryParamsAsString(rest);
  const queryKeyParams = getQueryParamsAsString(rest);

  return useQuery({
    queryKey: queryKeys.kubevision.namespaceDetail(organizationId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/namespaces-details/${namespaceId}?${params}`)
        .then(GetKubernetesNamespaceDetailResponse.fromJson)
        .then((res) => res.namespaceDetail);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesNodeDetailQuery = (
  data: Omit<PlainMessage<GetKubernetesNodeRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<KubernetesNode, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { nodeId, ...rest } = data;

  const params = getQueryParamsAsString(rest);
  const queryKeyParams = getQueryParamsAsString(rest);

  return useQuery({
    queryKey: queryKeys.kubevision.nodeDetail(organizationId, nodeId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/nodes/${nodeId}?${params}`)
        .then(GetKubernetesNodeResponse.fromJson)
        .then((res) => res.node);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesPodDetailQuery = (
  data: Omit<PlainMessage<GetKubernetesPodRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<KubernetesPodDetail, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { podId, ...rest } = data;

  const params = getQueryParamsAsString(rest);
  const queryKeyParams = getQueryParamsAsString(rest);

  return useQuery({
    queryKey: queryKeys.kubevision.podDetail(organizationId, podId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/pods/${podId}?${params}`)
        .then(GetKubernetesPodResponse.fromJson)
        .then((res) => res.pod);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesClusterDetailQuery = (
  data: Omit<PlainMessage<GetKubernetesClusterDetailRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<GetKubernetesClusterDetailResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const { instanceId, clusterId } = data;

  return useQuery({
    queryKey: queryKeys.kubevision.clusterDetail(organizationId, instanceId, clusterId).queryKey,
    queryFn: () => {
      return apiFetch(
        `orgs/${organizationId}/k8s/instances/${instanceId}/clusters/${clusterId}/detail`
      ).then(GetKubernetesClusterDetailResponse.fromJson);
    },
    ...refetchConfig,
    ...opts
  });
};

export const useListKubernetesDeprecatedAPIsQuery = (
  data: Omit<PlainMessage<ListKubernetesDeprecatedAPIsRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesDeprecatedAPIsResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);
  const queryKeyParams = getQueryParamsAsString(params);

  return useQuery({
    queryKey: queryKeys.kubevision.deprecatedAPIs(organizationId, queryKeyParams).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/deprecated-apis?${params}`).then(
        ListKubernetesDeprecatedAPIsResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesTimelineEventsQuery = (
  data: Omit<PlainMessage<ListKubernetesTimelineEventsRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesTimelineEventsResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.timelineEvents(organizationId, params).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/timeline-events?${params}`).then(
        ListKubernetesTimelineEventsResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesTimelineResourcesQuery = (
  data: Omit<PlainMessage<ListKubernetesTimelineResourcesRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListKubernetesTimelineResourcesResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  const params = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.kubevision.timelineResources(organizationId, params).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/timeline-resources?${params}`).then(
        ListKubernetesTimelineResourcesResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const useGetKubernetesSummaryQuery = (
  instanceId: string,
  opts?: UseQueryOptionsOverride<GetKubernetesSummaryResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  return useQuery({
    queryKey: queryKeys.kubevision.summary(organizationId, instanceId).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/k8s/summary?instanceId=${instanceId}`).then(
        GetKubernetesSummaryResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const useListInstances = (
  opts?: UseQueryOptionsOverride<ListInstancesResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  return useQuery({
    queryKey: queryKeys.kubevision.instances(organizationId).queryKey,
    queryFn: () =>
      apiFetch(`orgs/${organizationId}/argocd/instances`).then(ListInstancesResponse.fromJson),
    ...opts
  });
};

export const useGetInstanceQuery = (
  instanceId: string,
  opts?: UseQueryOptionsOverride<GetInstanceResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  return useQuery({
    queryKey: queryKeys.kubevision.instance(organizationId, instanceId).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/argocd/instances/${instanceId}`).then(
        GetInstanceResponse.fromJson
      );
    },
    ...refetchConfig,
    ...opts
  });
};

export const usePatchInstanceSpecMutation = () => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  return useMutation({
    mutationFn: ({ instanceId, data }: { instanceId: string; data: Subset<InstanceSpec> }) => {
      return apiFetch(`orgs/${organizationId}/argocd/instances/${instanceId}`, {
        method: 'PATCH',
        body: JSON.stringify({
          spec: data
        })
      });
    }
  });
};

export const useListIncidentsQuery = (
  data: Omit<PlainMessage<ListAIConversationsRequest>, 'organizationId' | 'incidentOnly'>,
  opts?: UseQueryOptionsOverride<ListAIConversationsResponse, readonly string[]>
) => {
  const { organizationId } = useKubeVisionContext();
  const { instanceId, ...rest } = data;
  const apiFetch = usePlatformApiFetch();
  const params = getQueryParamsAsString(rest);

  return useQuery({
    queryKey: queryKeys.kubevision.incidents(organizationId, instanceId, params).queryKey,
    queryFn: () => {
      return apiFetch(
        `orgs/${organizationId}/ai/conversations?incidentOnly=true&instanceId=${instanceId}&${params}`
      ).then(ListAIConversationsResponse.fromJson);
    },
    ...opts
  });
};

export const useGeIncidentQuery = (
  id: string,
  opts?: UseQueryOptionsOverride<AIConversation, readonly string[]>
) => {
  const { organizationId, instanceId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  return useQuery({
    queryKey: queryKeys.kubevision.incident(organizationId, id).queryKey,
    queryFn: () => {
      return apiFetch(`orgs/${organizationId}/ai/conversations/${id}?instanceId=${instanceId}`)
        .then(GetAIConversationResponse.fromJson)
        .then((res) => res.conversation);
    },
    ...opts
  });
};

export const useUpdateIncidentMutation = () => {
  const { organizationId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();

  return useMutation({
    mutationFn: (incident: UpdateAIConversationRequest) => {
      return apiFetch(`orgs/${organizationId}/ai/conversations/${incident.id}`, {
        method: 'PUT',
        body: JSON.stringify(incident.toJson())
      });
    }
  });
};

export const useListAIConversationsQuery = (
  data: Omit<PlainMessage<ListAIConversationsRequest>, 'organizationId' | 'instanceId'>,
  opts?: UseQueryOptionsOverride<ListAIConversationsResponse, readonly string[]>
) => {
  const { organizationId, instanceId } = useKubeVisionContext();
  const apiFetch = usePlatformApiFetch();
  const params = getQueryParamsAsString(data);

  return useQuery({
    queryKey: queryKeys.ai.conversations(organizationId, instanceId, params).queryKey,
    queryFn: () => {
      return apiFetch(
        `orgs/${organizationId}/ai/conversations?instanceId=${instanceId}&${params}`
      ).then(ListAIConversationsResponse.fromJson);
    },
    ...opts
  });
};
