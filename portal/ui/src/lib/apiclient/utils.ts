import { Timestamp } from '@bufbuild/protobuf';

import { paths } from '@ui/config/paths';

import { EnabledCluster } from './organization/v1/organization_pb';

// https://github.com/akuityio/akuity-platform/issues/1808
// delete secret request could have null string value (eg. {foo: null}) and is not supported in grpc request
// therefore we have used custom message instead of plain string
// new message is as { foo: { value: null }, bar: { value: 'secret' } }
export const transformGRPCCompatibleSecretBody = (
  secret: Record<string, string>
): Record<string, { value?: string }> => {
  const _secret: Record<string, { value?: string }> = {};

  for (const [key, value] of Object.entries(secret)) {
    _secret[key] = { value };
  }

  return _secret;
};

type AtLeastOneOfRequired<T, P extends keyof T = keyof T> = {
  [K in P]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<P, K>>>;
}[P];

type ClusterKeys = 'clusterId' | 'clusterName';

type OneOfClusterParamsType<T extends ClusterKeys = ClusterKeys> = AtLeastOneOfRequired<
  {
    clusterId: string;
    clusterName: string;
  },
  T
>;

type OneOfInstanceParamsType = AtLeastOneOfRequired<{
  instanceId: string;
  instanceName: string;
}>;

export class EnabledClustersInfo {
  private _enabledClusters: EnabledCluster[] = [];

  constructor(enabledClusters: EnabledCluster[]) {
    this._enabledClusters = enabledClusters;
  }

  instances(): { id: string; name: string }[] {
    return this.instanceIds()
      .map((id) => ({
        id,
        name: this.instanceName(id)
      }))
      .sort((a, b) => a.name.localeCompare(b.name));
  }

  instanceIds(): string[] {
    return [...new Set(this._enabledClusters.map((cluster) => cluster.instanceId))].sort((a, b) =>
      a.localeCompare(b)
    );
  }

  instanceId(instanceName: string): string {
    return (
      this._enabledClusters.find((cluster) => cluster.instanceName === instanceName)?.instanceId ||
      ''
    );
  }

  instanceNames(): string[] {
    return [...new Set(this._enabledClusters.map((cluster) => cluster.instanceName))].sort((a, b) =>
      a.localeCompare(b)
    );
  }

  instanceName(instanceId: string): string {
    return (
      this._enabledClusters.find((cluster) => cluster.instanceId === instanceId)?.instanceName || ''
    );
  }

  instanceShard(params: OneOfInstanceParamsType): string {
    return (
      this._enabledClusters.find(
        (cluster) =>
          cluster.instanceId === params.instanceId || cluster.instanceName === params.instanceName
      )?.shard || ''
    );
  }

  clusters(params?: OneOfInstanceParamsType): EnabledCluster[] {
    return (
      params?.instanceId || params?.instanceName
        ? this._enabledClusters.filter(
            (cluster) =>
              cluster.instanceId === params?.instanceId ||
              cluster.instanceName === params?.instanceName
          )
        : this._enabledClusters
    ).sort((a, b) => a.clusterName.localeCompare(b.clusterName));
  }

  clusterIds(params?: OneOfInstanceParamsType): string[] {
    return [
      ...new Set(
        params?.instanceId || params?.instanceName
          ? this._enabledClusters
              .filter(
                (cluster) =>
                  cluster.instanceId === params?.instanceId ||
                  cluster.instanceName === params?.instanceName
              )
              .map((cluster) => cluster.clusterId)
          : this._enabledClusters.map((cluster) => cluster.clusterId)
      )
    ].sort((a, b) => a.localeCompare(b));
  }

  clusterId(params: OneOfClusterParamsType<'clusterName'> & OneOfInstanceParamsType): string {
    return (
      this._enabledClusters.find(
        (cluster) =>
          (cluster.instanceName == params?.instanceName ||
            cluster.instanceId === params?.instanceId) &&
          cluster.clusterName === params.clusterName
      )?.clusterId || ''
    );
  }

  clusterNames(params?: OneOfInstanceParamsType): string[] {
    return [
      ...new Set(
        params?.instanceId || params?.instanceName
          ? this._enabledClusters
              .filter(
                (cluster) =>
                  cluster.instanceId === params?.instanceId ||
                  cluster.instanceName === params?.instanceName
              )
              .map((cluster) => cluster.clusterName)
          : this._enabledClusters.map((cluster) => cluster.clusterName)
      )
    ].sort((a, b) => a.localeCompare(b));
  }

  clusterName(clusterId: string): string {
    return (
      this._enabledClusters.find((cluster) => cluster.clusterId === clusterId)?.clusterName || ''
    );
  }

  clusterLastRefreshTime(params: OneOfClusterParamsType): string {
    return (
      this._enabledClusters.find(
        (cluster) =>
          cluster.clusterId === params.clusterId || cluster.clusterName === params.clusterName
      )?.lastRefreshTime || ''
    );
  }

  clusterLastConnectionTime(params: OneOfClusterParamsType): string {
    return (
      this._enabledClusters.find(
        (cluster) =>
          cluster.clusterId === params.clusterId || cluster.clusterName === params.clusterName
      )?.lastConnectTime || ''
    );
  }
}

export const getQueryParamsAsString = (data: unknown): string => {
  const search = new URLSearchParams();
  Object.entries(data)
    .sort((a, b) => a[0].localeCompare(b[0]))
    .forEach((item) => {
      const [key, value] = item;
      if (Array.isArray(value)) {
        value.forEach((v) => {
          if (v) search.append(key, v);
        });
        return;
      }
      if (value instanceof Timestamp) {
        search.append(key, value.toDate().toISOString());
        return;
      }
      if (value) {
        search.append(key, value);
        return;
      }
    });
  return search.toString();
};

export const getBaseUrl = (apiPathPrefix: string, shard?: string) => {
  let baseUrl = '';
  if (shard && !apiPathPrefix?.startsWith('/ext-api')) {
    // append shard to the baseUrl
    // http://portal-server.akuity-platform -> http://us1.portal-server.akuity-platform
    // We don't need to do this for /ext-api because it already runs on shard
    baseUrl = window.location.origin.replace(/^(https?:\/\/)/, `$1${shard}.`);
  }
  return baseUrl;
};

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
let refreshTokenRequest: Promise<Response | void> | null = null;

export const refreshToken = async () => {
  // Avoid parallel requests
  if (refreshTokenRequest === null) {
    refreshTokenRequest = fetch('/api/v1/auth/refresh-token', { method: 'POST' })
      .then((res) => {
        if (res.status === 403) {
          // If the refresh token request fails with 403, redirect to the login page
          window.location.href = paths.logout;
        }

        if (!res.ok) {
          throw new Error('Failed to refresh token');
        }
      })
      .finally(() => {
        // Clean up the promise
        refreshTokenRequest = null;
      });
  }

  return refreshTokenRequest;
};
