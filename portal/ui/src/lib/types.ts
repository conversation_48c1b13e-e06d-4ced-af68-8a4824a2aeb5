import { PlainMessage } from '@bufbuild/protobuf';
import {
  InfiniteData,
  Query<PERSON>ey,
  UseInfiniteQueryOptions,
  UseQueryOptions
} from '@tanstack/react-query';
import { z } from 'zod';

import {
  AIMessageContext,
  K8SNamespaceContext
} from '@ui/lib/apiclient/organization/v1/organization_pb';

import type { GetInstanceImageUpdaterSettingsResponse } from './apiclient';
import { ObjectFilter } from './apiclient/organization/v1/organization_pb';

export type Subset<K> = {
  [attr in keyof K]?: K[attr] extends object ? Subset<K[attr]> : K[attr];
};

export const uiPreferencesSchema = z.object({
  gettingStartedWizard: z.object({
    lastStepSeen: z.number().default(1),
    status: z.enum(['inProgress', 'done'])
  })
});

export enum OrganizationRole {
  Owner = 'owner',
  Member = 'member',
  None = ''
}

export interface ArgoCDInstancesConfigurationSummary {
  any: string;
  accountConfigured: string;
  clusterConnected: number;
  applicationCreated: number;
}

export interface ArgoCDImageUpdater extends GetInstanceImageUpdaterSettingsResponse {
  config: {
    'registries.conf'?: string;
    'git.user'?: string;
    'git.email'?: string;
    'git.commit-message-template'?: string;
    'log.level'?: ArgoCDImageUpdaterLogLevel;
  };
}

// react hook form doesn't support keys with '.' in update function
// it will treat it as a nested object, therefore we want to conver keys with '.' to '_'
export interface ArgoCDImageUpdaterForm extends GetInstanceImageUpdaterSettingsResponse {
  config: {
    registries_conf?: string;
    git_user?: string;
    git_email?: string;
    'git_commit-message-template'?: string;
    log_level?: ArgoCDImageUpdaterLogLevel;
  };
}

export type ArgoCDImageUpdaterLogLevel = 'info' | 'trace' | 'debug' | 'warn' | 'error';

export type IconSize =
  | 'xs'
  | 'sm'
  | 'lg'
  | '1x'
  | '2x'
  | '3x'
  | '4x'
  | '5x'
  | '6x'
  | '7x'
  | '8x'
  | '9x'
  | '10x';

export type Features = 'ui-settings-v2';

export interface SyncOperationStat {
  intervalStart: string;
  successfulCount: number;
  failedCount: number;
}

export enum GroupByInterval {
  Minute = 'minute',
  Hour = 'hour',
  Day = 'day',
  Week = 'week',
  Month = 'month',
  Year = 'year'
}

export type AuditFilterAction =
  | 'created'
  | 'updated'
  | 'deleted'
  | 'action-ran'
  | 'sync-started'
  | 'rollback-started';

export type AuditFilterObjectType =
  | 'organization_invite'
  | 'member'
  | 'argocd_instance'
  | 'argocd_cluster'
  | 'argocd_project'
  | 'argocd_application'
  | 'k8s_resource'
  | 'kargo_instance'
  | 'kargo_agent'
  | 'kargo_promotion'
  | 'kargo_freight'
  | 'custom_roles'
  | 'notification_cfg'
  | 'api_keys'
  | 'addons'
  | 'addon_marketplace_install'
  | 'addon_repos'
  | 'workspace';

export type RawAuditFilters = {
  // Pagination
  limit: number;
  offset: number;
  // Time
  start_time: string;
  end_time: string;
  // Object
  k8s_resource?: ObjectFilter | null;
  argocd_application?: ObjectFilter | null;
  argocd_cluster?: ObjectFilter | null;
  argocd_instance?: ObjectFilter | null;
  argocd_project?: ObjectFilter | null;
  member?: ObjectFilter | null;
  organization_invite?: ObjectFilter | null;
  kargo_instance?: ObjectFilter | null;
  kargo_agent?: ObjectFilter | null;
  kargo_promotion?: ObjectFilter | null;
  kargo_freight?: ObjectFilter | null;
  custom_roles?: ObjectFilter | null;
  notification_cfg?: ObjectFilter | null;
  api_keys?: ObjectFilter | null;
  addons?: ObjectFilter | null;
  addon_repos?: ObjectFilter | null;
  addon_marketplace_install?: ObjectFilter | null;
  workspace?: ObjectFilter | null;
  // Actor
  actor_id: string[];
  actor_type: string[];
  // Action
  action: string[];
};

export type UseQueryOptionsOverride<QueryResponse, QueryKeys extends QueryKey> = Omit<
  UseQueryOptions<QueryResponse, unknown, QueryResponse, QueryKeys>,
  'queryKey' | 'queryFn'
>;

export type UseInfiniteQueryOptionsOverride<
  QueryResponse,
  QueryKeys extends QueryKey,
  PageParam
> = Omit<
  UseInfiniteQueryOptions<
    QueryResponse,
    unknown,
    InfiniteData<QueryResponse>,
    QueryKeys,
    PageParam
  >,
  'queryKey' | 'queryFn' | 'getNextPageParam' | 'initialPageParam'
>;

export type MakeUseQueryOptions<Response> = Partial<UseQueryOptions<Response>>;

export type License = {
  applications: number;
  clusters: number;
  instances: number;
  kargoAgents: number;
  kargoInstances: number;
  kargoStages: number;
  /**
   * time after expiry, in seconds
   */
  gracePeriod: number;
  /**
   * time in unix timestamp seconds
   */
  expirationTime: number;
};

export type LicenseUsage = Pick<
  License,
  'applications' | 'instances' | 'clusters' | 'kargoAgents' | 'kargoInstances' | 'kargoStages'
>;

export enum WorkspaceAPIKeyRole {
  // IMPORTANT: keep the value in lowercase, thats what is expected in API
  Admin = 'admin',
  Member = 'member',
  None = ''
}

export type ExtendedAIMessageContext = Omit<PlainMessage<AIMessageContext>, 'k8sNamespace'> & {
  k8sNamespace?: PlainMessage<K8SNamespaceContext> & {
    clusterName?: string;
    instanceName?: string;
  };
};
