import { Button } from 'antd';
import React from 'react';
import { generatePath, useNavigate } from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { useGettingStartedTrigger } from '@ui/feature/user/getting-started/use-getting-started-trigger';
import { useJoinOrganization } from '@ui/lib/apiclient/organization/organization-queries';
import { Error, Loading, PageContent } from '@ui/lib/components';
import { useMainContext } from '@ui/lib/context/main-context';
import { useRequiredParams } from '@ui/lib/hooks/use-required-params';

export const OrganizationJoinPage = () => {
  const { orgID } = useRequiredParams<'orgID'>(['orgID']);
  const navigate = useNavigate();
  const ctx = useMainContext();
  const gettingStartedTrigger = useGettingStartedTrigger();
  const { mutate, data: org, isPending, error } = useJoinOrganization();

  React.useEffect(() => {
    if (ctx.user?.organizations.find((o) => o.id === orgID)) {
      navigate(generatePath(paths.organization, { id: orgID }), { replace: true });
    } else {
      mutate(
        { id: orgID },
        {
          onSuccess: async () => {
            await ctx.reloadUser();
            gettingStartedTrigger();
          }
        }
      );
    }
  }, [orgID, ctx.user.organizations]);

  return (
    <PageContent>
      {isPending ? (
        <Loading />
      ) : error ? (
        <Error err={error} />
      ) : (
        <>
          <h1 className='mb-4'>You successfully joined organization {org?.name}!</h1>
          <Button
            type='primary'
            onClick={() => navigate(generatePath(paths.organization, { id: org?.id }))}
          >
            Click to go to organization settings
          </Button>
        </>
      )}
    </PageContent>
  );
};
