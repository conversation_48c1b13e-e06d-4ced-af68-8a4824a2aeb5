import { faBoxArchive, faFileCsv } from '@fortawesome/free-solid-svg-icons';
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons/faInfoCircle';
import { faKey } from '@fortawesome/free-solid-svg-icons/faKey';
import { faShoePrints } from '@fortawesome/free-solid-svg-icons/faShoePrints';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Flex, Space } from 'antd';
import { Tag } from 'antd/lib';
import Button from 'antd/lib/button';
import Checkbox from 'antd/lib/checkbox/Checkbox';
import Table from 'antd/lib/table';
import Tooltip from 'antd/lib/tooltip';
import classNames from 'classnames';
import moment from 'moment';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { defaultAuditFilters } from '@ui/config/filters';
import { queryClient } from '@ui/config/query-client';
import { OrganizationMember } from '@ui/lib/apiclient/generated';
import {
  useGetAuditLogs,
  useGetOrganization
} from '@ui/lib/apiclient/organization/organization-queries';
import { queryKeys } from '@ui/lib/apiclient/query-keys';
import { FeatureStatus } from '@ui/lib/apiclient/types/features/v1/features_pb';
import { Error, IconLabel, Loading } from '@ui/lib/components';
import { AppliedFilters } from '@ui/lib/components/applied-filters';
import { FilteredIcon } from '@ui/lib/components/filter';
import { NewFeatureTag } from '@ui/lib/components/new-feature-tag';
import { PaidFeatureTag } from '@ui/lib/components/paid-feature-tag';
import { useMainContext } from '@ui/lib/context/main-context';
import { useModal } from '@ui/lib/hooks';
import { momentParse } from '@ui/lib/timezone';
import { RawAuditFilters } from '@ui/lib/types';
import { capitalize, omit, pick } from '@ui/lib/utils';

import { AuditLogArchiveDrawer } from '../audit-log-archive/audit-log-archive-drawer';
import { auditArchiveFeatureFreshness } from '../audit-log-archive/utils';

import { AuditLogDetailsModal } from './audit-log-details-modal';
import { AuditLogObject } from './audit-log-object';
import { ActionFilter } from './filters/action';
import { ActorFilter } from './filters/actor';
import { ObjectFilter } from './filters/object';
import { TimeFilter } from './filters/time';
import { useAuditFilter } from './filters/use-audit-filter';
import { auditFilterTransformer } from './filters/utils';
import './audit-log.less';
import { TFilterScopes } from './types';
import { shouldShowDetails } from './utils';

export type AuditLogProps = {
  members: OrganizationMember[];
  filterOverwrite?: (existingFilter: RawAuditFilters) => RawAuditFilters;
  filterScopes?: TFilterScopes;
  organizationId: string;
  hideArchive?: boolean;
};

export const AuditLog = ({
  members,
  filterOverwrite = (f) => f,
  filterScopes,
  organizationId,
  hideArchive
}: AuditLogProps) => {
  const id = organizationId;

  const initialSearchFilters = useRef(
    auditFilterTransformer.toRawAuditFilter(new URLSearchParams(window.location.search).toString())
  );

  const [search, setSearch] = useSearchParams();

  const auditFilterState = useMemo(() => {
    return auditFilterTransformer.toRawAuditFilter(search.toString());
  }, [search]);

  const [pageSize, setPageSize] = useState(initialSearchFilters.current?.limit || 20);
  const [page, setPage] = useState(
    initialSearchFilters.current?.offset ? initialSearchFilters.current?.offset / pageSize : 0
  );

  useEffect(() => {
    const newSearch = new URLSearchParams(search);

    newSearch.set('filters.offset', `${page * pageSize}`);
    newSearch.set('filters.limit', `${pageSize}`);

    setSearch(newSearch);
  }, [page, pageSize]);

  const {
    timeFilter: timefilterProps,
    actionFilter: actionfilterProps,
    actorFilter: actorfilterProps,
    objectFilter: objectfilterProps,
    appliedFilters
  } = useAuditFilter(auditFilterState, search, setSearch, filterScopes);

  const { data: organization, isLoading: isOrgLoading } = useGetOrganization({ id });

  const overwrittenAuditFilters = useMemo(
    () => filterOverwrite(auditFilterState),
    [auditFilterState]
  );

  const {
    data: auditLog,
    isLoading: isAuditLogLoading,
    error
  } = useGetAuditLogs({
    id,
    filters: {
      ...overwrittenAuditFilters,
      limit: pageSize,
      offset: page * pageSize
    }
  });

  //refetch the audit page every time when user switch back to audit tab
  useEffect(() => {
    if (search.get('tab') === 'audit') {
      queryClient.refetchQueries({
        queryKey: queryKeys.organizations.auditLogs(id).queryKey
      });
    }
  }, [search.get('tab')]);

  useEffect(() => {
    // case when user is on nth page, and change the filter and refresh the page
    // but filtered items are enough to get paginated until ith page where i < n
    if (auditLog && auditLog?.totalCount !== 0 && auditLog?.items.length === 0) {
      setPage(0);
    }
  }, [auditLog]);

  const exportCSV = (withAppliedFilters: boolean) => {
    const appliedFilters = withAppliedFilters
      ? { ...overwrittenAuditFilters }
      : { ...defaultAuditFilters, ...pick(overwrittenAuditFilters, ['limit', 'offset']) };
    const url = `/api/v1/organizations/${id}/csv-audit-logs?${auditFilterTransformer.toSearchString(appliedFilters)}`;
    window.open(url, '_blank');
  };

  const [exportWithFilters, setExportWithFilters] = useState(false);

  const { show: showDetailsModal } = useModal();
  const { show: showArchive } = useModal((p) => (
    <AuditLogArchiveDrawer {...p} organizationId={id} />
  ));

  const ctx = useMainContext();

  if (error) {
    return <Error err={error} />;
  }

  let availableActions: string[] = [];
  if (auditLog?.items) {
    const actions = new Set<string>();

    for (const item of auditLog.items) {
      actions.add(item.action);
    }

    availableActions = Array.from(actions);
  }

  return (
    <>
      <Flex className='mt-2 mb-6' justify='space-between'>
        <div>
          <IconLabel className='font-bold text-2xl mr-auto w-96' icon={faShoePrints}>
            Audit Log
          </IconLabel>
          <span className='text-xs font-light'>
            {organization?.quota?.auditRecordMonths ? (
              <>
                last {Number(organization?.quota?.auditRecordMonths)} months
                <Tooltip
                  title={
                    Number(organization?.quota?.auditRecordMonths)
                      ? `Logs older than ${Number(organization?.quota?.auditRecordMonths)} months are archived automatically.`
                      : ''
                  }
                >
                  <FontAwesomeIcon
                    icon={faInfoCircle}
                    className='ml-1 text-gray-300 cursor-pointer'
                  />
                </Tooltip>
              </>
            ) : null}
          </span>
        </div>

        <Space>
          <div className='audit-log-helpers__export-container'>
            {filterScopes !== 'instance' && filterScopes !== 'kargo_instance' && (
              <Checkbox
                checked={exportWithFilters}
                className='mt-0 mr-2 w-56 justify-end'
                onChange={(e) => setExportWithFilters(e.target.checked)}
              >
                Export filtered logs
              </Checkbox>
            )}
            <IconLabel
              icon={faFileCsv}
              className={classNames('audit-log-helpers__btn-export-csv', {
                'cursor-not-allowed text-gray-400':
                  ctx.currentOrgFeatureStatuses.auditRecordExport === FeatureStatus.DISABLED,
                'cursor-pointer hover:bg-gray-100':
                  ctx.currentOrgFeatureStatuses.auditRecordExport === FeatureStatus.ENABLED
              })}
              onClick={() => {
                if (ctx.currentOrgFeatureStatuses.auditRecordExport === FeatureStatus.ENABLED) {
                  exportCSV(
                    exportWithFilters ||
                      filterScopes === 'instance' ||
                      filterScopes === 'kargo_instance'
                  );
                }
              }}
            >
              <Flex>
                Export as CSV
                {ctx.currentOrgFeatureStatuses.auditRecordExport === FeatureStatus.DISABLED && (
                  <PaidFeatureTag className='ml-2' />
                )}
              </Flex>
            </IconLabel>
          </div>
          {!hideArchive && (
            <Button
              type='primary'
              onClick={() => showArchive()}
              icon={<FontAwesomeIcon icon={faBoxArchive} />}
              disabled={ctx.currentOrgFeatureStatuses.auditArchive === FeatureStatus.DISABLED}
            >
              Archive
              {ctx.currentOrgFeatureStatuses.auditArchive === FeatureStatus.DISABLED ? (
                <PaidFeatureTag />
              ) : (
                auditArchiveFeatureFreshness.isFeatureFresh() && <NewFeatureTag />
              )}
            </Button>
          )}
        </Space>
      </Flex>
      <AppliedFilters appliedFilters={appliedFilters} className='mb-5' />
      {isAuditLogLoading || isOrgLoading ? (
        <Loading />
      ) : (
        <>
          <Table
            size='small'
            rowClassName={(_, index) => (index % 2 === 0 ? '' : 'bg-gray-50')}
            columns={[
              {
                title: 'Time',
                width: 200,
                render: (_, record) => {
                  const parsedTimestamp = momentParse.withGlobalFormat(record.timestamp);
                  const readableTimestamp = parsedTimestamp.format('YYYY-MM-DD HH:mm:ss');

                  return (
                    <Tooltip title='Copy Timestamp'>
                      <div
                        className='font-mono text-sm cursor-pointer hover:text-blue-500'
                        onClick={() => window.navigator.clipboard.writeText(readableTimestamp)}
                      >
                        {moment().diff(parsedTimestamp, 'hours') > 24
                          ? readableTimestamp
                          : parsedTimestamp.fromNow()}
                      </div>
                    </Tooltip>
                  );
                },
                filterDropdown: () => (
                  <TimeFilter
                    onApplyFilter={() => {
                      const newSearch = auditFilterTransformer.toSearchString({
                        ...auditFilterState,
                        ...timefilterProps.raw
                      });

                      newSearch.set('tab', 'audit');
                      setSearch(newSearch);
                    }}
                    {...timefilterProps}
                  />
                ),
                filterIcon: FilteredIcon,
                filtered: Boolean(auditFilterState.start_time || auditFilterState.end_time)
              },
              {
                title: 'Object',
                render: (_, entry) => (
                  <>
                    <AuditLogObject showParentObject auditLog={entry} organization={organization} />
                  </>
                ),
                filterDropdown: () => (
                  <ObjectFilter
                    filterScopes={filterScopes}
                    onApplyFilter={() => {
                      const partialAuditFilterState = omit(auditFilterState, [
                        'k8s_resource',
                        'argocd_application',
                        'argocd_cluster',
                        'argocd_instance',
                        'argocd_project',
                        'member',
                        'organization_invite',
                        'kargo_instance',
                        'kargo_agent',
                        'kargo_freight',
                        'kargo_promotion',
                        'custom_roles',
                        'notification_cfg',
                        'api_keys',
                        'addons',
                        'addon_repos',
                        'addon_marketplace_install',
                        'workspace'
                      ]);
                      const newSearch = auditFilterTransformer.toSearchString({
                        ...partialAuditFilterState,
                        ...objectfilterProps.raw
                      });

                      newSearch.set('tab', 'audit');
                      setSearch(newSearch);
                    }}
                    {...objectfilterProps}
                  />
                ),
                filterIcon: FilteredIcon,
                filtered: Boolean(
                  auditFilterState?.k8s_resource?.enabled ||
                    auditFilterState?.argocd_application?.enabled ||
                    auditFilterState?.argocd_cluster?.enabled ||
                    auditFilterState?.argocd_instance?.enabled ||
                    auditFilterState?.argocd_project?.enabled ||
                    auditFilterState?.member?.enabled ||
                    auditFilterState?.organization_invite?.enabled ||
                    auditFilterState?.custom_roles?.enabled ||
                    auditFilterState?.addons?.enabled ||
                    auditFilterState?.addon_repos?.enabled ||
                    auditFilterState?.addon_marketplace_install?.enabled
                )
              },
              {
                title: 'Actor',
                render: (_, entry) => {
                  let actorType: ReactNode = '';

                  switch (entry.actor.type) {
                    case 'argocd_user':
                      actorType = <u>(ArgoCD User)</u>;
                      break;
                    case 'kargo_actor':
                      actorType = <u>(Kargo)</u>;
                      break;
                    case 'akuity_token':
                      actorType = (
                        <u>
                          (API key
                          <FontAwesomeIcon icon={faKey} className='text-[12px] ml-1' />)
                        </u>
                      );
                      break;
                  }

                  return (
                    <span>
                      {entry.actor.id} {actorType}
                    </span>
                  );
                },
                filterDropdown: () => (
                  <ActorFilter
                    onApplyFilter={() => {
                      const newSearch = auditFilterTransformer.toSearchString({
                        ...auditFilterState,
                        actor_id: actorfilterProps.filterActor
                      });

                      newSearch.set('tab', 'audit');

                      setSearch(newSearch);
                    }}
                    suggestedActors={members.map((member) => member.email)}
                    {...actorfilterProps}
                  />
                ),
                filterIcon: FilteredIcon,
                filtered: Boolean(auditFilterState.actor_id.length)
              },
              {
                title: 'Action',
                render: (_, entry) => <span>{capitalize(entry.action)}</span>,
                filterDropdown: () => (
                  <ActionFilter
                    onApplyFilter={() => {
                      const newSearch = auditFilterTransformer.toSearchString({
                        ...auditFilterState,
                        ...actionfilterProps.raw
                      });

                      newSearch.set('tab', 'audit');
                      setSearch(newSearch);
                    }}
                    availableActions={availableActions}
                    {...actionfilterProps}
                  />
                ),
                filterIcon: FilteredIcon,
                filtered: Boolean(auditFilterState.action.length)
              },
              {
                title: 'Details',
                width: 50,
                render: (_, entry) => (
                  <div className='flex'>
                    {shouldShowDetails(entry) ? (
                      <Button
                        icon={<FontAwesomeIcon icon={faInfoCircle} />}
                        onClick={() =>
                          showDetailsModal((p) => (
                            <AuditLogDetailsModal
                              {...p}
                              auditDetails={entry}
                              organization={organization}
                            />
                          ))
                        }
                        type='text'
                        size='small'
                        className='text-gray-500'
                      />
                    ) : null}
                    {entry.count > 1 && (
                      <Tooltip
                        className='ml-2'
                        title={
                          <>
                            This event was observed <b>{entry.count}</b> times between{' '}
                            <u>
                              {momentParse
                                .withGlobalFormat(entry.timestamp)
                                .format('MMM Do yyyy hh:mm:ss')}
                            </u>{' '}
                            to{' '}
                            <u>
                              {momentParse
                                .withGlobalFormat(entry.lastOccurredTimestamp)
                                .format('MMM Do yyyy hh:mm:ss')}
                            </u>
                            .
                          </>
                        }
                      >
                        <Tag>{entry.count} times</Tag>
                      </Tooltip>
                    )}
                  </div>
                )
              }
            ]}
            dataSource={auditLog.items.map((item, i) => ({ ...item, key: i }))}
            pagination={{
              pageSize,
              current: page + 1,
              onChange: (page) => setPage(page - 1),
              onShowSizeChange: (_, size) => setPageSize(size),
              hideOnSinglePage: false,
              showSizeChanger: true,
              total: auditLog.totalCount
            }}
          />
        </>
      )}
    </>
  );
};
