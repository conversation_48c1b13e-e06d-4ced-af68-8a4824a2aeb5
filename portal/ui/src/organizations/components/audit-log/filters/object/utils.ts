import { ObjectFilter } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { AuditFilterObjectType, RawAuditFilters } from '@ui/lib/types';

import { CommonInputProps } from './common-input';

export const auditObjectMapper: { [key in AuditFilterObjectType]: string } = {
  argocd_application: 'Argo CD Application',
  argocd_cluster: 'Argo CD Cluster',
  argocd_instance: 'Argo CD Instance',
  argocd_project: 'Argo CD Project',
  k8s_resource: 'K8S Resource',
  member: 'Member',
  organization_invite: 'Organization Invite',
  kargo_instance: 'Kargo Instance',
  kargo_agent: 'Kargo Agent',
  kargo_freight: 'Kargo Freight',
  kargo_promotion: 'Kargo Promotion',
  custom_roles: 'Custom Roles',
  notification_cfg: 'Notification Config',
  api_keys: 'Api Keys',
  addons: 'Addons',
  addon_repos: 'Addon Repos',
  addon_marketplace_install: 'Addon Marketplace Installs',
  workspace: 'Workspace'
};

export type ObjectType = {
  name: string;
  type: AuditFilterObjectType | '';
  group: string;
  kind: string;
  parentId: string;
  parentParentId: string;
};

export const cleanObject: ObjectType = {
  name: '',
  type: '',
  group: '',
  kind: '',
  parentId: '',
  parentParentId: ''
};

export const toObjectType = (rawFilters: RawAuditFilters): ObjectType => {
  let existingType: keyof RawAuditFilters = 'k8s_resource';

  if (rawFilters?.k8s_resource?.enabled) {
    existingType = 'k8s_resource';
  } else if (rawFilters?.argocd_application?.enabled) {
    existingType = 'argocd_application';
  } else if (rawFilters?.argocd_instance?.enabled) {
    existingType = 'argocd_instance';
  } else if (rawFilters?.argocd_cluster?.enabled) {
    existingType = 'argocd_cluster';
  } else if (rawFilters?.member?.enabled) {
    existingType = 'member';
  } else if (rawFilters?.organization_invite?.enabled) {
    existingType = 'organization_invite';
  } else if (rawFilters?.kargo_instance?.enabled) {
    existingType = 'kargo_instance';
  } else if (rawFilters?.kargo_agent?.enabled) {
    existingType = 'kargo_agent';
  } else if (rawFilters?.kargo_freight?.enabled) {
    existingType = 'kargo_freight';
  } else if (rawFilters?.kargo_promotion?.enabled) {
    existingType = 'kargo_promotion';
  } else if (rawFilters?.custom_roles?.enabled) {
    existingType = 'custom_roles';
  } else if (rawFilters?.notification_cfg?.enabled) {
    existingType = 'notification_cfg';
  } else if (rawFilters?.api_keys?.enabled) {
    existingType = 'api_keys';
  } else if (rawFilters?.addons?.enabled) {
    existingType = 'addons';
  } else if (rawFilters?.addon_repos?.enabled) {
    existingType = 'addon_repos';
  } else if (rawFilters?.addon_marketplace_install?.enabled) {
    existingType = 'addon_marketplace_install';
  } else if (rawFilters?.workspace?.enabled) {
    existingType = 'workspace';
  } else {
    return {
      type: '',
      group: '',
      kind: '',
      name: '',
      parentId: '',
      parentParentId: ''
    };
  }

  const data: ObjectType = {
    type: existingType,
    group: rawFilters?.[existingType]?.objectGroup[0],
    kind: rawFilters?.[existingType]?.objectKind[0],
    name: rawFilters?.[existingType]?.objectName[0],
    parentId: rawFilters?.[existingType]?.objectParentName[0],
    parentParentId: rawFilters?.[existingType]?.objectParentParentName[0]
  };

  return data;
};

export const toRawObject = (
  object: ObjectType
): Pick<
  RawAuditFilters,
  | 'k8s_resource'
  | 'argocd_application'
  | 'argocd_instance'
  | 'argocd_cluster'
  | 'argocd_project'
  | 'member'
  | 'organization_invite'
  | 'kargo_instance'
  | 'kargo_agent'
  | 'kargo_freight'
  | 'kargo_promotion'
  | 'custom_roles'
  | 'notification_cfg'
  | 'api_keys'
  | 'addons'
  | 'addon_repos'
  | 'addon_marketplace_install'
  | 'workspace'
> => {
  const data = new ObjectFilter({
    enabled: true,
    objectName: [object.name].filter(Boolean),
    objectGroup: [object.group].filter(Boolean),
    objectKind: [object.kind].filter(Boolean),
    objectParentName: [object.parentId].filter(Boolean),
    objectParentParentName: [object.parentParentId].filter(Boolean)
  });

  switch (object.type) {
    case 'k8s_resource':
      return {
        k8s_resource: data
      };
    case 'argocd_application':
      return {
        argocd_application: data
      };
    case 'argocd_cluster':
      return {
        argocd_cluster: data
      };
    case 'argocd_instance':
      return {
        argocd_instance: data
      };
    case 'argocd_project':
      return {
        argocd_project: data
      };
    case 'member':
      return {
        member: data
      };
    case 'organization_invite':
      return {
        organization_invite: data
      };
    case 'kargo_instance':
      return {
        kargo_instance: data
      };
    case 'kargo_agent':
      return {
        kargo_agent: data
      };
    case 'kargo_freight':
      return {
        kargo_freight: data
      };
    case 'kargo_promotion':
      return {
        kargo_promotion: data
      };
    case 'custom_roles':
      return {
        custom_roles: data
      };
    case 'notification_cfg':
      return {
        notification_cfg: data
      };
    case 'api_keys':
      return {
        api_keys: data
      };
    case 'addons':
      return {
        addons: data
      };
    case 'addon_repos':
      return {
        addon_repos: data
      };
    case 'addon_marketplace_install':
      return {
        addon_marketplace_install: data
      };
    case 'workspace':
      return {
        workspace: data
      };
  }
};

export const metadataForTypeAndInput = (
  type: AuditFilterObjectType | '',
  input: keyof Omit<ObjectType, 'type'>
): {
  shouldRender: boolean;
  metadataProps: Pick<CommonInputProps, 'label' | 'type' | 'placeholder'>;
} => {
  if (!type) {
    return {
      shouldRender: false,
      metadataProps: {}
    };
  }

  let metadataProps: ReturnType<typeof metadataForTypeAndInput>['metadataProps'] = {};
  switch (type) {
    case 'organization_invite': {
      return {
        shouldRender: input === 'name',
        metadataProps: {
          label: 'Invitee email',
          type: 'email',
          placeholder: '<EMAIL>'
        }
      };
    }
    case 'member': {
      return {
        shouldRender: input === 'name',
        metadataProps: {
          label: 'Email',
          type: 'email',
          placeholder: '<EMAIL>'
        }
      };
    }
    case 'argocd_instance': {
      return {
        shouldRender: input === 'name',
        metadataProps: {
          label: 'Instance name',
          placeholder: 'instance-1'
        }
      };
    }
    case 'argocd_cluster': {
      if (input === 'name') {
        metadataProps = {
          label: 'Cluster name',
          placeholder: 'cluster-1'
        };
      } else if (input === 'parentId') {
        metadataProps = {
          label: 'Connected instance name',
          placeholder: 'instance-1'
        };
      }

      return {
        shouldRender: input === 'name' || input === 'parentId',
        metadataProps
      };
    }
    case 'argocd_application': {
      if (input === 'name') {
        metadataProps = {
          label: 'Application name',
          placeholder: 'guestbook'
        };
      } else if (input === 'parentId') {
        metadataProps = {
          label: 'Argo CD instance name',
          placeholder: 'instance-1'
        };
      }

      return {
        shouldRender: input === 'name' || input === 'parentId',
        metadataProps
      };
    }
    case 'argocd_project': {
      if (input === 'name') {
        metadataProps = {
          label: 'Project name',
          placeholder: 'default'
        };
      } else if (input === 'parentId') {
        metadataProps = {
          label: 'Argo CD instance name',
          placeholder: 'instance-1'
        };
      }

      return {
        shouldRender: input === 'name' || input === 'parentId',
        metadataProps
      };
    }
    case 'k8s_resource': {
      if (input === 'name') {
        metadataProps = {
          label: 'Resource name',
          placeholder: 'guestbook-ui'
        };
      }

      if (input === 'group') {
        metadataProps = {
          label: 'Group',
          placeholder: 'app'
        };
      }

      if (input === 'kind') {
        metadataProps = {
          label: 'Kind',
          placeholder: 'Deployment'
        };
      }

      if (input === 'parentId') {
        metadataProps = {
          label: 'Cluster name',
          placeholder: 'cluster-1'
        };
      }

      return {
        shouldRender: true, // should render all inputs,
        metadataProps
      };
    }

    case 'kargo_promotion':
    case 'kargo_freight': {
      if (input === 'name') {
        metadataProps = {
          label: 'Name',
          placeholder: 'name'
        };
      } else if (input === 'parentId') {
        metadataProps = {
          label: 'Stage name',
          placeholder: 'stage-1'
        };
      }

      return {
        shouldRender: input === 'name' || input === 'parentId',
        metadataProps
      };
    }

    case 'kargo_instance':
    case 'kargo_agent': {
      if (input === 'name') {
        metadataProps = {
          label: 'Name',
          placeholder: 'name'
        };
      }

      return {
        shouldRender: input === 'name',
        metadataProps
      };
    }

    case 'notification_cfg': {
      if (input === 'name') {
        metadataProps = {
          label: 'Name',
          placeholder: 'name'
        };
      }

      return {
        shouldRender: input === 'name',
        metadataProps
      };
    }

    case 'api_keys': {
      if (input === 'name') {
        metadataProps = {
          label: 'Name',
          placeholder: 'name'
        };
      }

      return {
        shouldRender: input === 'name',
        metadataProps
      };
    }

    case 'addons': {
      if (input === 'name') {
        metadataProps = {
          label: 'Name',
          placeholder: 'kustomize-example-addons'
        };
      }

      if (input === 'parentId') {
        metadataProps = {
          label: 'Repo URL',
          placeholder: 'https://<addon-repo-url>'
        };
      }

      if (input === 'parentParentId') {
        metadataProps = {
          label: 'Instance',
          placeholder: '<instance-name>'
        };
      }

      return {
        shouldRender: input === 'name' || input === 'parentId' || input === 'parentParentId',
        metadataProps
      };
    }

    case 'addon_repos': {
      if (input === 'name') {
        metadataProps = {
          label: 'Repo URL',
          placeholder: 'https://<addon-repo-url>'
        };
      }

      if (input === 'parentId') {
        metadataProps = {
          label: 'Instance',
          placeholder: '<instance-name>'
        };
      }

      return {
        shouldRender: input === 'name' || input === 'parentId',
        metadataProps
      };
    }

    case 'addon_marketplace_install': {
      if (input === 'name') {
        metadataProps = {
          label: 'Name',
          placeholder: 'kustomize-example-addons'
        };
      }

      return {
        shouldRender: input === 'name',
        metadataProps
      };
    }
    case 'workspace': {
      if (input === 'name') {
        metadataProps = {
          label: 'Workspace name',
          placeholder: 'my-workspace'
        };
      }

      return {
        shouldRender: input === 'name',
        metadataProps
      };
    }
  }
};
