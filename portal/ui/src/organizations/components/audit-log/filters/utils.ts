import { PlainMessage } from '@bufbuild/protobuf';

import { defaultAuditFilters } from '@ui/config/filters';
import type { AuditFilters } from '@ui/lib/apiclient/generated';
import { ObjectFilter } from '@ui/lib/apiclient/organization/v1/organization_pb';
import type { RawAuditFilters } from '@ui/lib/types';
import { flattenObject, omit } from '@ui/lib/utils';

import type { timerangeOpts, timerangeState } from './shared';

export const auditFilterKeys: Record<keyof AuditFilters, string> = {
  action: 'filters.action',
  actorId: 'filters.actorId',
  actorType: 'filters.actorType',
  endTime: 'filters.endTime',
  limit: 'filters.limit',
  k8sResource: 'filters.k8sResource',
  argocdApplication: 'filters.argocdApplication',
  argocdCluster: 'filters.argocdCluster',
  argocdInstance: 'filters.argocdInstance',
  argocdProject: 'filters.argocdProject',
  member: 'filters.member',
  organizationInvite: 'filters.organizationInvite',
  offset: 'filters.offset',
  startTime: 'filters.startTime',
  kargoInstance: 'filters.kargoInstance',
  kargoAgent: 'filters.kargoAgent',
  kargoPromotion: 'filters.kargoPromotion',
  kargoFreight: 'filters.kargoFreight',
  customRoles: 'filters.customRoles',
  notificationCfg: 'filters.notificationCfg',
  apiKeys: 'filters.apiKeys',
  addons: 'filters.addons',
  addonRepos: 'filters.addonRepos',
  addonMarketplaceInstall: 'filters.addonMarketplaceInstall',
  workspace: 'filters.workspace',
  workspaceMember: 'filters.workspaceMember'
};

export const customTimeRange = (type: timerangeOpts): timerangeState => {
  const state: timerangeState = {
    startTime: new Date(),
    endTime: new Date()
  };

  switch (type) {
    case '7-days':
      state.startTime.setDate(state.endTime.getDate() - 7);
      break;
    case '1-month':
      state.startTime.setMonth(state.endTime.getMonth() - 1);
      break;
    case '1-year':
      state.startTime.setFullYear(state.endTime.getFullYear() - 1);
      break;
  }
  return state;
};

export const auditFilterTransformer = {
  toRawAuditFilter: (searchString: string): RawAuditFilters => {
    const search = new URLSearchParams(searchString);

    const rawAuditFilters: Partial<RawAuditFilters> = {};

    const limit = search.get(auditFilterKeys.limit);
    if (limit) {
      rawAuditFilters['limit'] = Number(limit);
    }

    const offset = search.get(auditFilterKeys.offset);
    if (offset) {
      rawAuditFilters['offset'] = Number(offset);
    }

    const start_time = search.get(auditFilterKeys.startTime);
    if (start_time) {
      rawAuditFilters['start_time'] = start_time;
    }

    const end_time = search.get(auditFilterKeys.endTime);
    if (end_time) {
      rawAuditFilters['end_time'] = end_time;
    }

    const actor_id = search.getAll(auditFilterKeys.actorId);
    if (actor_id?.length) {
      rawAuditFilters['actor_id'] = actor_id;
    }

    const actor_type = search.getAll(auditFilterKeys.actorType);
    if (actor_type?.length) {
      rawAuditFilters['actor_type'] = actor_type;
    }

    const action = search.getAll(auditFilterKeys.action);
    if (action?.length) {
      rawAuditFilters['action'] = action;
    }

    for (const objectKey of objects) {
      let auditFilterKey: keyof AuditFilters;
      switch (objectKey) {
        case 'argocd_application':
          auditFilterKey = 'argocdApplication';
          break;
        case 'argocd_cluster':
          auditFilterKey = 'argocdCluster';
          break;
        case 'argocd_instance':
          auditFilterKey = 'argocdInstance';
          break;
        case 'argocd_project':
          auditFilterKey = 'argocdProject';
          break;
        case 'k8s_resource':
          auditFilterKey = 'k8sResource';
          break;
        case 'member':
          auditFilterKey = 'member';
          break;
        case 'organization_invite':
          auditFilterKey = 'organizationInvite';
          break;
        case 'kargo_instance':
          auditFilterKey = 'kargoInstance';
          break;
        case 'kargo_agent':
          auditFilterKey = 'kargoAgent';
          break;
        case 'kargo_promotion':
          auditFilterKey = 'kargoPromotion';
          break;
        case 'kargo_freight':
          auditFilterKey = 'kargoFreight';
          break;
        case 'custom_roles':
          auditFilterKey = 'customRoles';
          break;
        case 'notification_cfg':
          auditFilterKey = 'notificationCfg';
          break;
        case 'api_keys':
          auditFilterKey = 'apiKeys';
          break;
        case 'addons':
          auditFilterKey = 'addons';
          break;
        case 'addon_repos':
          auditFilterKey = 'addonRepos';
          break;
        case 'addon_marketplace_install':
          auditFilterKey = 'addonMarketplaceInstall';
          break;
        case 'workspace':
          auditFilterKey = 'workspace';
          break;
      }

      rawAuditFilters[objectKey] = new ObjectFilter({});

      if (search.get(`${auditFilterKeys[auditFilterKey]}.enabled`) === 'true') {
        rawAuditFilters[objectKey].enabled = true;
        for (const resource of objectResources) {
          const resourceValues = search.getAll(`${auditFilterKeys[auditFilterKey]}.${resource}`);

          if (resourceValues?.length) {
            (rawAuditFilters[objectKey][resource] as string[]) = resourceValues;
          }
        }
      }
    }

    return { ...defaultAuditFilters, ...rawAuditFilters };
  },
  toSearchString: (rawAuditFilters: RawAuditFilters, _excludeKeys?: string[]): URLSearchParams => {
    const excludeKeys = _excludeKeys || [];
    const search = new URLSearchParams();
    const _append = (key: string, items: string[]) => {
      for (const item of items) {
        search.append(key, item);
      }
    };

    if (!excludeKeys.includes(auditFilterKeys.limit) && rawAuditFilters.limit) {
      search.set(auditFilterKeys.limit, rawAuditFilters.limit.toString());
    }

    if (!excludeKeys.includes(auditFilterKeys.offset) && rawAuditFilters.offset) {
      search.set(auditFilterKeys.offset, rawAuditFilters.offset.toString());
    }

    if (!excludeKeys.includes(auditFilterKeys.startTime) && rawAuditFilters.start_time) {
      search.set(auditFilterKeys.startTime, rawAuditFilters.start_time);
    }

    if (!excludeKeys.includes(auditFilterKeys.endTime) && rawAuditFilters.end_time) {
      search.set(auditFilterKeys.endTime, rawAuditFilters.end_time);
    }

    if (!excludeKeys.includes(auditFilterKeys.actorId) && rawAuditFilters.actor_id?.length) {
      _append(auditFilterKeys.actorId, rawAuditFilters.actor_id);
    }

    if (!excludeKeys.includes(auditFilterKeys.actorType) && rawAuditFilters.actor_type?.length) {
      _append(auditFilterKeys.actorType, rawAuditFilters.actor_type);
    }

    if (!excludeKeys.includes(auditFilterKeys.action) && rawAuditFilters.action?.length) {
      _append(auditFilterKeys.action, rawAuditFilters.action);
    }

    const resources: Array<{
      key: string;
      rawKey: TObjectTypes;
    }> = [
      {
        key: auditFilterKeys.k8sResource,
        rawKey: 'k8s_resource'
      },
      {
        key: auditFilterKeys.argocdApplication,
        rawKey: 'argocd_application'
      },
      {
        key: auditFilterKeys.argocdInstance,
        rawKey: 'argocd_instance'
      },
      {
        key: auditFilterKeys.argocdProject,
        rawKey: 'argocd_project'
      },
      {
        key: auditFilterKeys.argocdCluster,
        rawKey: 'argocd_cluster'
      },
      {
        key: auditFilterKeys.member,
        rawKey: 'member'
      },
      {
        key: auditFilterKeys.organizationInvite,
        rawKey: 'organization_invite'
      },
      {
        key: auditFilterKeys.kargoInstance,
        rawKey: 'kargo_instance'
      },
      {
        key: auditFilterKeys.kargoAgent,
        rawKey: 'kargo_agent'
      },
      {
        key: auditFilterKeys.kargoFreight,
        rawKey: 'kargo_freight'
      },
      {
        key: auditFilterKeys.kargoPromotion,
        rawKey: 'kargo_promotion'
      },
      {
        key: auditFilterKeys.customRoles,
        rawKey: 'custom_roles'
      },
      {
        key: auditFilterKeys.notificationCfg,
        rawKey: 'notification_cfg'
      },
      {
        key: auditFilterKeys.apiKeys,
        rawKey: 'api_keys'
      },
      {
        key: auditFilterKeys.addons,
        rawKey: 'addons'
      },
      {
        key: auditFilterKeys.addonRepos,
        rawKey: 'addon_repos'
      },
      {
        key: auditFilterKeys.addonMarketplaceInstall,
        rawKey: 'addon_marketplace_install'
      },
      {
        key: auditFilterKeys.workspace,
        rawKey: 'workspace'
      }
    ];

    for (const resource of resources) {
      if (!excludeKeys.includes(resource.key) && rawAuditFilters?.[resource.rawKey]?.enabled) {
        search.set(`${resource.key}.enabled`, 'true');

        const objects = flattenObject(omit(rawAuditFilters?.[resource.rawKey], ['enabled']));

        for (const [flatObjectKey, value] of Object.entries(objects)) {
          _append(`${resource.key}.${flatObjectKey}`, value as string[]);
        }
      }
    }

    return search;
  }
};

type TObjectTypes = keyof Pick<
  RawAuditFilters,
  | 'k8s_resource'
  | 'argocd_application'
  | 'argocd_instance'
  | 'argocd_project'
  | 'argocd_cluster'
  | 'member'
  | 'organization_invite'
  | 'kargo_instance'
  | 'kargo_agent'
  | 'kargo_freight'
  | 'kargo_promotion'
  | 'custom_roles'
  | 'notification_cfg'
  | 'api_keys'
  | 'addons'
  | 'addon_repos'
  | 'addon_marketplace_install'
  | 'workspace'
>;

const objects: Array<TObjectTypes> = [
  'k8s_resource',
  'argocd_application',
  'argocd_cluster',
  'argocd_instance',
  'argocd_project',
  'member',
  'organization_invite',
  'kargo_instance',
  'kargo_agent',
  'kargo_freight',
  'kargo_promotion',
  'custom_roles',
  'notification_cfg',
  'api_keys',
  'addons',
  'addon_repos',
  'addon_marketplace_install',
  'workspace'
];

const objectResources: Array<keyof PlainMessage<ObjectFilter>> = [
  'objectGroup',
  'objectKind',
  'objectName',
  'objectParentApplicationName',
  'objectParentName',
  'objectParentParentName'
];
