import { PlainMessage } from '@bufbuild/protobuf';
import { Alert } from 'antd/lib';

import {
  useGetKubernetesEnabledClustersQuery,
  useGetKubernetesNodesQuery,
  useGetKubernetesPodsQuery,
  useListKubernetesNamespacesDetailQuery
} from '@ui/lib/apiclient/organization/kubevision-queries';
import {
  KubernetesNode,
  KubernetesPod,
  NamespaceDetail,
  NamespaceGroupBy,
  NodeGroupBy,
  PodGroupBy
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { PageTitle } from '@ui/lib/components/page-title';
import { useKubeVisionContext } from '@ui/lib/context/kubevision-context';

import { heatmapView } from '../../const';
import { mergeFilter } from '../../filter';
import useCustomSearchParams from '../../hooks/use-custom-search-params';
import { isClusterExpired } from '../../utils';
import { KubeVisionCheckEnabledClusters } from '../shared/kubevision-check-enabled-clusters';
import { KubeVisionDegradedClustersWarning } from '../shared/kubevision-degraded-clusters-warning';

import { KubeVisionInfrastructureDrawer } from './kubevision-infrastructure-drawer';
import {
  defaultInfrastructureFilter,
  InfrastructureFilter
} from './kubevision-infrastructure-filter';
import { KubeVisionInfrastructureFilterBar } from './kubevision-infrastructure-filter-bar';
import { KubernetesInfrastructureHeatmap } from './kubevision-infrastructure-heatmap';

type Props = {
  instanceId: string;
};

export const KubeVisionInfrastructureDashboard = ({ instanceId }: Props) => {
  const { enabledClustersInfo } = useKubeVisionContext();
  const { getSearchParam, setSearchParams } = useCustomSearchParams();

  const params = [
    'view',
    'groupBy',
    'nodeFiller',
    'podFiller',
    'search',
    'nodeId',
    'nodeInstance',
    'nodeCluster',
    'podId',
    'podInstance',
    'podCluster',
    'namespaceFiller',
    'namespaceId',
    'namespaceInstance',
    'namespaceCluster',
    'clusterCluster',
    'where',
    'cluster'
  ];

  const initFilter: Record<string, string | number> = {};

  for (const param of params) {
    if (getSearchParam(param)) initFilter[param] = getSearchParam(param);
  }
  initFilter.groupBy = Number(initFilter.groupBy || 1);
  initFilter.nodeFiller = Number(initFilter.nodeFiller || 1);
  initFilter.podFiller = Number(initFilter.podFiller || 1);
  initFilter.namespaceFiller = Number(initFilter.namespaceFiller || 1);
  initFilter.cluster = initFilter.cluster || '';

  const infrastructureFilter = mergeFilter(defaultInfrastructureFilter(), initFilter);
  const handleFilterChange = (
    filter: Partial<InfrastructureFilter>,
    overwrite?: boolean,
    replace?: boolean
  ) => {
    const newFilter = overwrite
      ? mergeFilter(defaultInfrastructureFilter(), filter)
      : mergeFilter(infrastructureFilter, filter);

    const params: Record<string, string | undefined> = {
      groupBy: newFilter?.groupBy?.toString(),
      search: newFilter?.search?.toString(),
      view: newFilter?.view?.toString(),
      where: newFilter?.where?.toString(),
      cluster: newFilter?.cluster?.toString()
    };

    // clear all params that are not needed for the new view
    // Nodes
    params.nodeFiller = undefined;
    params.nodeId = undefined;
    params.nodeInstance = undefined;
    params.nodeCluster = undefined;
    // Pods
    params.podFiller = undefined;
    params.podId = undefined;
    params.podInstance = undefined;
    params.podCluster = undefined;
    // Namespaces
    params.namespaceFiller = undefined;
    params.namespaceId = undefined;
    params.namespaceInstance = undefined;
    params.namespaceCluster = undefined;
    // Cluster
    params.clusterCluster = undefined;

    if (newFilter?.view === heatmapView.Nodes) {
      params.nodeFiller = newFilter?.nodeFiller?.toString();
      params.nodeId = newFilter?.nodeId?.toString();
      params.nodeInstance = newFilter?.nodeInstance?.toString();
      params.nodeCluster = newFilter?.nodeCluster?.toString();
    } else if (newFilter?.view === heatmapView.Pods) {
      params.podFiller = newFilter?.podFiller?.toString();
      params.podId = newFilter?.podId?.toString();
      params.podInstance = newFilter?.podInstance?.toString();
      params.podCluster = newFilter?.podCluster?.toString();
    } else if (newFilter?.view === heatmapView.Namespaces) {
      params.namespaceFiller = newFilter?.namespaceFiller?.toString();
      params.namespaceId = newFilter?.namespaceId?.toString();
      params.namespaceInstance = newFilter?.namespaceInstance?.toString();
      params.namespaceCluster = newFilter?.namespaceCluster?.toString();
    }

    params.clusterCluster = newFilter?.clusterCluster?.toString();

    setSearchParams(params, { replace });
  };

  const { data: clustersData } = useGetKubernetesEnabledClustersQuery(
    { instanceId },
    {
      staleTime: 5000
    }
  );

  const hasMetricServerUnavailable = clustersData?.some(
    (cluster) => cluster.metricServerUnavailable && !isClusterExpired(cluster.lastConnectTime)
  );

  const isNodeView = infrastructureFilter.view === heatmapView.Nodes;
  const isPodView = infrastructureFilter.view === heatmapView.Pods;
  const isNamespaceView = infrastructureFilter.view === heatmapView.Namespaces;

  const { data: nodesData, isLoading: isNodesLoading } = useGetKubernetesNodesQuery(
    {
      instanceId,
      clusterIds: [
        enabledClustersInfo.clusterId({
          instanceId,
          clusterName: infrastructureFilter.cluster
        })
      ],
      groupBy: [infrastructureFilter.groupBy] as NodeGroupBy[],
      filler: infrastructureFilter.nodeFiller
    },
    {
      enabled: clustersData?.length > 0 && isNodeView
    }
  );

  const { data: podsData, isLoading: isPodsLoading } = useGetKubernetesPodsQuery(
    {
      instanceId,
      clusterIds: [
        enabledClustersInfo.clusterId({
          instanceId,
          clusterName: infrastructureFilter.cluster
        })
      ],
      groupBy: [infrastructureFilter.groupBy] as PodGroupBy[],
      filler: infrastructureFilter.podFiller
    },
    {
      enabled: clustersData?.length > 0 && isPodView
    }
  );

  const { data: namespacesData, isLoading: isNamespacesLoading } =
    useListKubernetesNamespacesDetailQuery(
      {
        instanceId,
        clusterIds: [
          enabledClustersInfo.clusterId({
            instanceId,
            clusterName: infrastructureFilter.cluster
          })
        ],
        groupBy: [infrastructureFilter.groupBy] as NamespaceGroupBy[],
        filler: infrastructureFilter.namespaceFiller
      },
      {
        enabled: clustersData?.length > 0 && isNamespaceView,
        refetchInterval: 0
      }
    );

  return (
    <>
      <PageTitle>Akuity Intelligence Infrastructure</PageTitle>

      <KubeVisionInfrastructureDrawer
        instanceId={enabledClustersInfo.instanceId(
          isNodeView
            ? infrastructureFilter.nodeInstance
            : isPodView
              ? infrastructureFilter.podInstance
              : infrastructureFilter.namespaceInstance
        )}
        filter={infrastructureFilter}
        onFilterChange={handleFilterChange}
      />

      {hasMetricServerUnavailable && (
        <Alert
          message='Metrics Server Unavailable'
          description='Up-to-date resource metrics are not available for some clusters due to the unavailability of the metrics server.'
          type='warning'
          showIcon
          className='mb-4'
          banner
        />
      )}

      {enabledClustersInfo.clusters().some((cluster) => cluster.isDegraded) && (
        <KubeVisionDegradedClustersWarning enabledClustersInfo={enabledClustersInfo} />
      )}

      <KubeVisionInfrastructureFilterBar
        instanceId={instanceId}
        filter={infrastructureFilter}
        onFilterChange={handleFilterChange}
      />
      <KubeVisionCheckEnabledClusters>
        {isNodesLoading || isPodsLoading || isNamespacesLoading ? (
          <Loading />
        ) : (
          <div className='pb-2'>
            <KubernetesInfrastructureHeatmap
              heatmapData={isNodeView ? nodesData : isPodView ? podsData : namespacesData}
              filter={infrastructureFilter}
              handleSelectedItem={(item) => {
                if (isNodeView) {
                  const node = item as PlainMessage<KubernetesNode>;
                  handleFilterChange({
                    nodeId: node.id,
                    nodeInstance: enabledClustersInfo.instanceName(node.instanceId),
                    nodeCluster: node.clusterName,
                    where: 'nodeName=' + node.name
                  });
                } else if (isPodView) {
                  const pod = item as PlainMessage<KubernetesPod>;
                  handleFilterChange({
                    podId: pod.id,
                    podInstance: enabledClustersInfo.instanceName(pod.instanceId),
                    podCluster: pod.clusterName
                  });
                } else if (isNamespaceView) {
                  const namespace = item as PlainMessage<NamespaceDetail>;
                  handleFilterChange({
                    namespaceId: namespace.id,
                    namespaceInstance: enabledClustersInfo.instanceName(namespace.instanceId),
                    namespaceCluster: enabledClustersInfo.clusterName(namespace.clusterId)
                  });
                }
              }}
              handleSelectedCluster={(clusterId, instanceId) => {
                const filterParams: Partial<InfrastructureFilter> = {
                  clusterCluster: enabledClustersInfo.clusterName(clusterId)
                };

                if (isNodeView) {
                  filterParams.nodeInstance = enabledClustersInfo.instanceName(instanceId);
                } else if (isPodView) {
                  filterParams.podInstance = enabledClustersInfo.instanceName(instanceId);
                } else if (isNamespaceView) {
                  filterParams.namespaceInstance = enabledClustersInfo.instanceName(instanceId);
                }

                handleFilterChange(filterParams);
              }}
            />
          </div>
        )}
      </KubeVisionCheckEnabledClusters>
    </>
  );
};
