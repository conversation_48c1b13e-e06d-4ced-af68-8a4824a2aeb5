import { faMagnifyingGlassChart } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Row, Tag } from 'antd';
import React, { ReactNode } from 'react';

interface KubevisionInfrastructureClusterProps {
  children: ReactNode;
  clusterName: string;
  itemCount: number;
  itemType?: string;
  isDarkTheme: boolean;
  isMetricServerUnavailable?: boolean;
  isDegraded?: boolean;
  isExpired?: boolean;
  fillValue?: string;
  onClick?: (e: React.MouseEvent) => void;
  customStyle?: React.CSSProperties;
  isPodView?: boolean;
}

export const KubevisionInfrastructureCluster: React.FC<KubevisionInfrastructureClusterProps> = ({
  children,
  clusterName,
  itemCount,
  itemType = '',
  isDarkTheme,
  isMetricServerUnavailable = false,
  isExpired = false,
  isDegraded = false,
  fillValue,
  onClick,
  customStyle,
  isPodView = false
}) => {
  const getItemLabel = () => {
    if (!itemType) return '';

    const suffix = itemCount === 1 ? '' : 's';
    return `${itemType}${suffix}`;
  };

  return (
    <div
      className={`mt-5 mr-5 px-5 pt-5 pb-3 ${isPodView ? 'h-full' : ''} border rounded-lg ${
        isDarkTheme ? 'bg-[#141414]' : 'bg-white hover:bg-[#fafafa]'
      } hover:border-[#fafafa] hover:shadow-lg`}
      style={{ ...customStyle, minWidth: 'fit-content' }}
    >
      <Row
        wrap={false}
        justify='space-between'
        align='middle'
        className={`mb-6 px-4 py-1 min-w-fit m-full bg-gray-200 ${isMetricServerUnavailable || isExpired ? '' : 'hover:bg-gray-100'} rounded-2xl ${isMetricServerUnavailable || isExpired ? 'cursor-not-allowed' : 'cursor-pointer'}`}
        onClick={isMetricServerUnavailable || isExpired ? undefined : onClick}
      >
        <div className='flex items-center whitespace-nowrap'>
          {clusterName && <strong>{clusterName}:</strong>}
          <span className='ml-1'>
            {itemCount} {getItemLabel()} {fillValue && <span> - {fillValue}</span>}
          </span>

          {isMetricServerUnavailable && !isExpired && (
            <Tag
              color='red'
              bordered={false}
              className='ml-2 rounded-xl text-[8px] !leading-[14px]'
            >
              metrics server unavailable
            </Tag>
          )}

          {isDegraded && !isExpired && (
            <Tag
              color='red'
              bordered={false}
              className='ml-2 rounded-xl text-[8px] !leading-[14px]'
            >
              failed to sync
            </Tag>
          )}

          {isExpired && (
            <Tag
              color='red'
              bordered={false}
              className='ml-2 rounded-xl text-[8px] !leading-[14px]'
            >
              Last connection is 7 days ago
            </Tag>
          )}
        </div>
        <FontAwesomeIcon className='ml-8' icon={faMagnifyingGlassChart} color='grey' />
      </Row>
      {children}
    </div>
  );
};
