import { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';

import {
  clearQueryParams,
  generatePrefixedParams,
  mergeFilter
} from '@ui/feature/kubevision/filter';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { useGetKubernetesResourceTypesQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { HealthStatus } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { PageTitle } from '@ui/lib/components/page-title';
import { useKubeVisionContext } from '@ui/lib/context/kubevision-context';

import { TabType } from '../../const';
import { KubeVisionCheckEnabledClusters } from '../shared/kubevision-check-enabled-clusters';
import { KubeVisionDegradedClustersWarning } from '../shared/kubevision-degraded-clusters-warning';
import { isEventTimelineEnabled } from '../shared/kubevision-event-timeline/helper';

import { KubeVisionExplorerDrawer } from './kubevision-explorer-drawer';
import { defaultExplorerFilter, type ExplorerFilter } from './kubevision-explorer-filter';
import { KubeVisionExplorerFilterBar } from './kubevision-explorer-filter-bar';
import { KubeVisionExplorerTable, KubeVisionExplorerTableRef } from './kubevision-explorer-table';
import { TreeViewBtn } from './tree-view-btn';

type Props = {
  instanceId: string;
};

export const KubeVisionExplorerDashboard = ({ instanceId }: Props) => {
  const { organizationMode, enabledClustersInfo } = useKubeVisionContext();
  const { getSearchParam, getSearchParamPagination, setSearchParams } = useCustomSearchParams();
  const explorerTableRef = useRef<KubeVisionExplorerTableRef>(null);

  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const initFilter: Record<string, string | number | boolean | string[] | HealthStatus[]> = {};
  const baseParams = [
    'resourceId',
    'resourceInstance',
    'resourceCluster',
    'group',
    'version',
    'kind',
    'ownerId',
    'instance',
    'cluster',
    'namespace',
    'nameContains',
    'treeView',
    'detailsTab',
    'treeViewHealthStatuses',
    'treeViewResourceKinds',
    'treeViewNameContains'
  ];

  const logsParams = generatePrefixedParams(TabType.Logs, [
    'content',
    'pod',
    'container',
    'tailLines'
  ]);
  const eventTimelineParams = generatePrefixedParams(TabType.EventTimeline, [
    'startTime',
    'endTime',
    'range',
    'eventType',
    'severity',
    'orderBy',
    'limit',
    'offset'
  ]);

  const params = [...baseParams, ...logsParams, ...eventTimelineParams];

  for (const param of params) {
    if (getSearchParam(param)) {
      if (param === 'treeView') {
        initFilter['treeView'] = getSearchParam(param) === 'true';
      } else if (param === 'treeViewHealthStatuses') {
        const healthStatusStr = getSearchParam(param);
        initFilter['treeViewHealthStatuses'] = healthStatusStr
          ? healthStatusStr
              .split(',')
              .map((status) => parseInt(status, 10))
              .filter((status) => !isNaN(status))
          : [];
      } else if (param === 'treeViewResourceKinds') {
        const resourceKindsStr = getSearchParam(param);
        initFilter['treeViewResourceKinds'] = resourceKindsStr ? resourceKindsStr.split(',') : [];
      } else {
        initFilter[param] = getSearchParam(param);
      }
    }
  }
  // they must be assigned together
  if (initFilter['group'] || initFilter['version'] || initFilter['kind']) {
    initFilter['group'] = initFilter['group'] ?? '';
    initFilter['version'] = initFilter['version'] ?? '';
    initFilter['kind'] = initFilter['kind'] ?? '';
  }
  const { limit, offset, orderBy } = getSearchParamPagination();
  initFilter.limit = limit;
  initFilter.offset = offset;
  initFilter.orderBy = orderBy;

  const explorerFilter = mergeFilter(defaultExplorerFilter(), initFilter);

  const { data: resourceTypes } = useGetKubernetesResourceTypesQuery(instanceId);

  const handleTableFilterChange = (filter: Partial<ExplorerFilter>, overwrite?: boolean) => {
    const newFilter = overwrite
      ? mergeFilter(defaultExplorerFilter(), filter)
      : mergeFilter(explorerFilter, filter);

    let filterToUse = newFilter;

    // clear logs params
    if (newFilter.detailsTab !== TabType.Logs) {
      filterToUse = clearQueryParams(filterToUse, TabType.Logs);
    }

    // clear eventTimeline params
    if (newFilter.detailsTab !== TabType.EventTimeline) {
      filterToUse = clearQueryParams(filterToUse, TabType.EventTimeline);
    }

    // clear namespace params when kind is clusterScoped in list
    if (!newFilter.treeView) {
      const isClusterScoped = resourceTypes?.resourceTypes?.find(
        (item) =>
          item.groupVersionKind.group === newFilter?.group &&
          item.groupVersionKind.version === newFilter?.version &&
          item.groupVersionKind.kind === newFilter?.kind
      )?.clusterScoped;

      if (isClusterScoped && newFilter.namespace) {
        filterToUse = clearQueryParams(filterToUse, 'namespace');
      }
    }

    // Convert array properties to string for URL parameters
    const searchParams: Record<string, string> = {
      ...filterToUse,
      instance: organizationMode ? filterToUse?.instance : undefined,
      treeView: filterToUse?.treeView?.toString(),
      limit: filterToUse?.limit?.toString(),
      offset: filterToUse?.offset?.toString(),
      orderBy: filterToUse?.orderBy?.toString(),
      treeViewHealthStatuses: filterToUse?.treeViewHealthStatuses
        ? filterToUse.treeViewHealthStatuses.join(',')
        : undefined,
      treeViewResourceKinds: filterToUse?.treeViewResourceKinds
        ? filterToUse.treeViewResourceKinds.join(',')
        : undefined,
      treeViewNameContains: filterToUse?.treeViewNameContains
    };

    setSearchParams(searchParams);
  };

  return (
    <>
      <PageTitle>Akuity Intelligence Explorer</PageTitle>

      <KubeVisionExplorerDrawer
        instanceId={enabledClustersInfo.instanceId(explorerFilter.resourceInstance)}
        filter={explorerFilter}
        onFilterChange={handleTableFilterChange}
      />

      {isMounted &&
        ReactDOM.createPortal(
          <TreeViewBtn
            explorerTableRef={explorerTableRef}
            onFilterChange={handleTableFilterChange}
          />,
          document.getElementById('kubevision-dashboard-header-tree-view-btn') as HTMLElement
        )}
      {isMounted &&
        enabledClustersInfo.clusters().some((cluster) => cluster.isDegraded) &&
        ReactDOM.createPortal(
          <KubeVisionDegradedClustersWarning enabledClustersInfo={enabledClustersInfo} />,
          document.getElementById('kubevision-dashboard-header-degraded-status') as HTMLElement
        )}
      <KubeVisionExplorerFilterBar
        instanceId={instanceId}
        filter={explorerFilter}
        onFilterChange={handleTableFilterChange}
      />

      <KubeVisionCheckEnabledClusters>
        <KubeVisionExplorerTable
          ref={explorerTableRef}
          instanceId={instanceId}
          filter={explorerFilter}
          onFilterChange={handleTableFilterChange}
          onClickResource={(record) =>
            handleTableFilterChange({
              resourceId: record.uid,
              resourceInstance: enabledClustersInfo.instanceName(record.instanceId),
              resourceCluster: enabledClustersInfo.clusterName(record.clusterId),
              detailsTab: isEventTimelineEnabled(record.group, record.kind)
                ? TabType.EventTimeline
                : TabType.Manifest
            })
          }
        />
      </KubeVisionCheckEnabledClusters>
    </>
  );
};
