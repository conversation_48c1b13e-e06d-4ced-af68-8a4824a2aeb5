import { Timestamp } from '@bufbuild/protobuf';
import moment, { isMoment, Moment } from 'moment';

export const getAPIVersion = ({ group, version }: { group: string; version: string }) => {
  return group ? group + '/' + version : version;
};

export const getGroupVersion = (apiVersion: string) => {
  if (apiVersion) {
    return { group: apiVersion.split('/')[0], version: apiVersion.split('/')[1] || '' };
  }
  return {
    group: '',
    version: ''
  };
};

export function getSeverityColor(severity: string): string {
  if (severity?.toUpperCase() == 'CRITICAL') {
    return 'red';
  }
  if (severity?.toUpperCase() == 'HIGH') {
    return 'volcano';
  }
  if (severity?.toUpperCase() == 'MEDIUM') {
    return 'gold';
  }
  return 'default';
}

export function formatTime(
  time: Moment | Timestamp | Date | string | number,
  opts?: { useUTCTime?: boolean; format?: string; fromNow?: boolean }
) {
  if (!time) {
    return 'N/A';
  }
  if (time instanceof Timestamp) {
    time = time.toDate();
  }
  const momentTime = isMoment(time) ? time : moment(time);
  const adjustedTime = opts?.useUTCTime ? momentTime.utc() : momentTime.local();
  return opts?.fromNow
    ? adjustedTime.fromNow()
    : adjustedTime.format(opts?.format ?? 'YYYY-MM-DD HH:mm:ss Z');
}

export function isClusterExpired(lastConnectTime?: string): boolean {
  if (!lastConnectTime) {
    return false;
  }

  const lastConnectMoment = moment(lastConnectTime);
  const oneWeekAgo = moment().subtract(7, 'days');

  return lastConnectMoment.isBefore(oneWeekAgo);
}
