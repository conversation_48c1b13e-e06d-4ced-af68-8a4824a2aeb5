import { useCallback, useEffect } from 'react';

import { useNativeSearchParams } from '@ui/lib/hooks/use-native-search-params';
import { useLocalStorage } from '@ui/lib/utils';

export const useAISupportEngineerDrawer = () => {
  const [search, setSearch] = useNativeSearchParams();
  const chatVals = search.getAll('akuity-chat');
  const [lastOpenConversationId, setLastOpenConversationId] = useLocalStorage('akuity-chat', '');
  const isOpen = chatVals.length > 0;
  const conversationId = isOpen ? chatVals[0] : undefined;

  const clearIncidentParams = (searchParams: URLSearchParams) => {
    Array.from(searchParams.keys())
      .filter((key) => key.startsWith('akuity-chat-') && key !== 'akuity-chat-history')
      .forEach((key) => searchParams.delete(key));
  };

  const clearSelection = () => {
    const newSearch = new URLSearchParams(window.location.search);
    newSearch.set('akuity-chat', '');
    setLastOpenConversationId('');
    setSearch(newSearch);
  };

  const setIsOpen = (isOpen: boolean, conversationId?: string) => {
    const newSearch = new URLSearchParams(window.location.search);
    if (isOpen) {
      newSearch.set('akuity-chat', conversationId || '');
      setLastOpenConversationId(conversationId || '');
    } else {
      newSearch.delete('akuity-chat');
      clearIncidentParams(newSearch);
    }
    setSearch(newSearch);
  };

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Check for Command + Shift + L
    if (event.metaKey && event.shiftKey && event.key.toLowerCase() === 'l') {
      event.preventDefault();
      setIsOpen(false);
    }
  }, []);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return {
    isOpen,
    conversationId,
    lastOpenConversationId,
    clearSelection,
    setIsOpen
  };
};
