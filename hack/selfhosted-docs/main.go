package main

import (
	"bufio"
	"flag"
	"fmt"
	"os"
	"sort"
	"strings"

	"github.com/go-logr/logr"
	"gopkg.in/yaml.v3"

	"github.com/akuityio/akuity-platform/internal/utils/misc"
)

const imagesFilePath = "../docs.akuity.io/docs/100-self-hosted/30-images-list.md"

func searchImageKeys(data interface{}, images map[string]bool) {
	switch v := data.(type) {
	case map[string]interface{}:
		if imageVal, ok := v["image"]; ok {
			if imageStr, ok := imageVal.(string); ok && imageStr != "" {
				if !isExcludedImage(imageStr) {
					images[imageStr] = true
				}
			}
		}
		for _, val := range v {
			searchImageKeys(val, images)
		}
	case []interface{}:
		for _, item := range v {
			searchImageKeys(item, images)
		}
	}
}

// processFile opens a YAML file, decodes all its documents,
// and extracts image values using searchImageKeys.
func processFile(path string, images map[string]bool) error {
	f, err := os.Open(path)
	if err != nil {
		return err
	}
	defer f.Close()

	dec := yaml.NewDecoder(f)
	for {
		var doc interface{}
		err = dec.Decode(&doc)
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			fmt.Fprintf(os.Stderr, "Error decoding file %s: %v. Skipping file...\n", path, err)
			break
		}
		searchImageKeys(doc, images)
	}
	return nil
}

func listArgoCDVersions() {
	log := logr.Logger{}
	argoVersions, err := misc.GetArgoCDVersions(log)
	if err != nil {
		panic(err)
	}
	vers := make([]string, 0, len(argoVersions))
	for _, v := range argoVersions {
		vers = append(vers, v.Version)
	}
	sort.Strings(vers)
	fmt.Printf("%s\n", strings.Join(vers, ","))
}

func listKargoVersions() {
	log := logr.Logger{}
	kargoVersions, kargoUnstableVersion, err := misc.GetKargoVersions(log)
	if err != nil {
		panic(err)
	}
	vers := make([]string, 0, len(kargoVersions))
	for _, v := range kargoVersions {
		vers = append(vers, v.Version)
	}
	if kargoUnstableVersion != nil {
		vers = append(vers, kargoUnstableVersion.Version)
	}
	sort.Strings(vers)
	fmt.Printf("%s\n", strings.Join(vers, ","))
}

// isExcludedImage returns true if image matches one of the excluded patterns.
func isExcludedImage(image string) bool {
	excludePrefixes := []string{
		"quay.io/argoproj/argocd:",
		"quay.io/akuity/argocd:",
		"ghcr.io/akuity/kargo:",
		"quay.io/akuity/agent:",
		"us-docker.pkg.dev/akuity/akp/agent-server:",
		"us-docker.pkg.dev/akuity/akp-sh/agent-server:",
		"ghcr.io/akuity/kargo-unstable:",
		"quay.io/argoprojlabs/argocd-image-updater:",
		"quay.io/akuity/kargo-unstable:",
		"quay.io/akuity/kargo:",
		"postgres:",
	}
	for _, prefix := range excludePrefixes {
		if strings.HasPrefix(image, prefix) {
			return true
		}
	}
	return false
}

func writeImages(images map[string]bool, akpVersion string) {
	fmt.Println("Writing images to file...")
	newFile := "30-images-list.md"

	data, err := os.ReadFile(imagesFilePath)
	if err != nil {
		fmt.Printf("Error reading file: %v\n", err)
		return
	}
	content := string(data)

	versionHeader := fmt.Sprintf("### %s", akpVersion)
	if strings.Contains(content, versionHeader) {
		// If it already exists, just write out the file verbatim
		fmt.Printf("Version %s already exists in the image list. Reusing current content\n", akpVersion)
		err = os.WriteFile(newFile, []byte(content), 0o644)
		if err != nil {
			fmt.Printf("Failed to write file: %v\n", err)
			return
		}
		return
	}

	var imgList []string
	for img := range images {
		imgList = append(imgList, img)
	}
	sort.Strings(imgList)

	newSection := "\n" + versionHeader
	for _, img := range imgList {
		newSection += fmt.Sprintf("\n- %s", img)
	}

	var lines []string
	scanner := bufio.NewScanner(strings.NewReader(content))
	targetHeading := "## Image List"

	for scanner.Scan() {
		line := scanner.Text()
		lines = append(lines, line)
		// When we see the target heading, insert the new section immediately after.
		if strings.TrimSpace(line) == targetHeading {
			lines = append(lines, newSection)
		}
	}
	if err := scanner.Err(); err != nil {
		fmt.Printf("Scanner error: %v\n", err)
		return
	}

	updatedContent := strings.Join(lines, "\n")
	err = os.WriteFile(newFile, []byte(updatedContent), 0o644)
	if err != nil {
		fmt.Printf("Failed to write file: %v\n", err)
		return
	}

	fmt.Println("Markdown file updated.")
}

func main() {
	argocdFlag := flag.Bool("export-argocd-versions", false, "List all argocd versions")
	kargoFlag := flag.Bool("export-kargo-versions", false, "List all kargo versions")
	akpVersionFlag := flag.String("akp-version", "latest", "AKP version to use for generating image list")
	flag.Parse()

	if *argocdFlag {
		listArgoCDVersions()
		return
	}
	if *kargoFlag {
		listKargoVersions()
		return
	}

	images := make(map[string]bool)
	files := flag.Args()

	for _, file := range files {
		if err := processFile(file, images); err != nil {
			fmt.Fprintf(os.Stderr, "Error processing file %s: %v\n", file, err)
		}
	}

	writeImages(images, *akpVersionFlag)
}
