swagger: "2.0"
info:
  title: aims/v1/aims.proto
  version: version not set
tags:
  - name: AimsService
consumes:
  - application/json
produces:
  - application/json
paths:
  /api/v1/aims/argo/instances:
    get:
      operationId: AimsService_ListArgoInstances
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ListArgoInstancesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: filter.paid
          in: query
          required: false
          type: boolean
        - name: filter.unpaid
          in: query
          required: false
          type: boolean
        - name: filter.fuzz
          description: search by instance name/id
          in: query
          required: false
          type: string
        - name: filter.timeFrom
          in: query
          required: false
          type: string
        - name: filter.organizationId
          description: filter by organization ID
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/instances/{instanceId}:
    get:
      operationId: AimsService_GetInstanceById
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/GetInstanceByIdResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
      tags:
        - AimsService
    delete:
      summary: buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
      operationId: AimsService_DeleteUnpaidInstance
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/DeleteUnpaidInstanceResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
        - name: audit.actor
          in: query
          required: false
          type: string
        - name: audit.reason
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/instances/{instanceId}/clusters:
    get:
      operationId: AimsService_ListClustersForInstance
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ListClustersForInstanceResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
        - name: filter.fuzz
          description: search by name/id/namespace
          in: query
          required: false
          type: string
        - name: filter.timeFrom
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/instances/{instanceId}/clusters/{clusterId}:
    post:
      operationId: AimsService_InstanceClusterMaintenance
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/InstanceClusterMaintenanceResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
        - name: clusterId
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/InstanceClusterMaintenanceBody'
      tags:
        - AimsService
  /api/v1/aims/instances/{instanceId}/clusters/{clusterId}/manifests:
    get:
      summary: |-
        buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
        buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
      operationId: AimsService_GetClusterManifests
      responses:
        "200":
          description: A successful response.(streaming responses)
          schema:
            type: string
            format: binary
            properties: {}
            title: Free form byte stream
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
        - name: clusterId
          in: path
          required: true
          type: string
        - name: audit.actor
          in: query
          required: false
          type: string
        - name: audit.reason
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/instances/{instanceId}/generation/decrement:
    post:
      operationId: AimsService_DecrementInstanceGeneration
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/DecrementInstanceGenerationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/DecrementInstanceGenerationBody'
      tags:
        - AimsService
  /api/v1/aims/internal-audit-logs:
    get:
      operationId: AimsService_GetInternalAuditLogs
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/GetInternalAuditLogsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: filters.actorId
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.objectType
          in: query
          required: false
          type: string
        - name: filters.action
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.startTime
          in: query
          required: false
          type: string
        - name: filters.endTime
          in: query
          required: false
          type: string
        - name: filters.limit
          in: query
          required: false
          type: integer
          format: int64
        - name: filters.offset
          in: query
          required: false
          type: integer
          format: int64
      tags:
        - AimsService
  /api/v1/aims/internal/config:
    get:
      operationId: AimsService_GetInternalConfig
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/GetInternalConfigResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      tags:
        - AimsService
  /api/v1/aims/kargo/instances:
    get:
      operationId: AimsService_ListKargoInstances
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.ListKargoInstancesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: filter.paid
          in: query
          required: false
          type: boolean
        - name: filter.unpaid
          in: query
          required: false
          type: boolean
        - name: filter.fuzz
          description: search by instance name/id
          in: query
          required: false
          type: string
        - name: filter.timeFrom
          in: query
          required: false
          type: string
        - name: filter.organizationId
          description: filter by organization ID
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/kargo/instances/{instanceId}:
    get:
      operationId: AimsService_GetKargoInstanceById
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/GetKargoInstanceByIdResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
      tags:
        - AimsService
    delete:
      summary: |-
        buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
        buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
        buf:lint:ignore RPC_REQUEST_STANDARD_NAME
      operationId: AimsService_DeleteUnpaidKargoInstance
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/DeleteUnpaidInstanceResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
        - name: audit.actor
          in: query
          required: false
          type: string
        - name: audit.reason
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/kargo/instances/{instanceId}/agents:
    get:
      operationId: AimsService_ListAgentsForKargoInstance
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ListAgentsForKargoInstanceResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
      tags:
        - AimsService
  /api/v1/aims/kargo/instances/{instanceId}/agents/{agentId}/manifests:
    get:
      summary: |-
        buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
        buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
      operationId: AimsService_GetKargoAgentManifests
      responses:
        "200":
          description: A successful response.(streaming responses)
          schema:
            type: string
            format: binary
            properties: {}
            title: Free form byte stream
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: instanceId
          in: path
          required: true
          type: string
        - name: agentId
          in: path
          required: true
          type: string
        - name: audit.actor
          in: query
          required: false
          type: string
        - name: audit.reason
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/mfa/reset:
    post:
      operationId: AimsService_ResetMFA
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ResetMFAResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/ResetMFARequest'
      tags:
        - AimsService
  /api/v1/aims/notification:
    post:
      operationId: AimsService_SendNotification
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/SendNotificationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/SendNotificationRequest'
      tags:
        - AimsService
  /api/v1/aims/onboard:
    post:
      operationId: AimsService_OnboardManualCustomer
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/OnboardManualCustomerResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/OnboardManualCustomerRequest'
      tags:
        - AimsService
  /api/v1/aims/organizations:
    get:
      operationId: AimsService_ListAllOrganizations
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ListAllOrganizationsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: filters.limit
          description: default limit 50
          in: query
          required: false
          type: integer
          format: int64
        - name: filters.offset
          in: query
          required: false
          type: integer
          format: int64
        - name: filters.fuzz
          description: |-
            fuzzy search
            by name
            by id
            by member
          in: query
          required: false
          type: string
        - name: filters.sortByCreation
          in: query
          required: false
          type: string
          enum:
            - SORT_UNSPECIFIED
            - SORT_ASCENDING
            - SORT_DESCENDING
          default: SORT_UNSPECIFIED
        - name: filters.billed
          in: query
          required: false
          type: boolean
        - name: filters.manuallyVerified
          in: query
          required: false
          type: boolean
        - name: filters.plans
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.startTime
          in: query
          required: false
          type: string
        - name: filters.endTime
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/organizations/instance_creation:
    post:
      operationId: AimsService_SetDisabledInstanceCreation
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/SetDisabledInstanceCreationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/SetDisabledInstanceCreationRequest'
      tags:
        - AimsService
  /api/v1/aims/organizations/members:
    get:
      operationId: AimsService_ListOrganizationMembers
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.ListOrganizationMembersResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
      tags:
        - AimsService
  /api/v1/aims/organizations/unbilled:
    get:
      operationId: AimsService_ListUnbilledOrganizations
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ListUnbilledOrganizationsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      tags:
        - AimsService
  /api/v1/aims/organizations/{id}/feature-gates:
    get:
      operationId: AimsService_GetFeatureGates
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/GetFeatureGatesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: id
          in: path
          required: true
          type: string
      tags:
        - AimsService
    patch:
      operationId: AimsService_PatchFeatureGates
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/PatchFeatureGatesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: id
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/PatchFeatureGatesBody'
      tags:
        - AimsService
  /api/v1/aims/organizations/{id}/quotas:
    put:
      operationId: AimsService_UpdateQuotas
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/UpdateQuotasResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: id
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UpdateQuotasBody'
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}:
    get:
      operationId: AimsService_GetOrganization
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.GetOrganizationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
      tags:
        - AimsService
    delete:
      operationId: AimsService_DeleteOrganization
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.DeleteOrganizationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: audit.actor
          in: query
          required: false
          type: string
        - name: audit.reason
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/ai/conversations:
    get:
      operationId: AimsService_ListAIConversations
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ListAIConversationsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: instanceId
          in: query
          required: false
          type: string
        - name: incidentOnly
          in: query
          required: false
          type: boolean
        - name: incidentStatus
          in: query
          required: false
          type: string
          enum:
            - INCIDENT_STATUS_UNSPECIFIED
            - INCIDENT_STATUS_RESOLVED
            - INCIDENT_STATUS_UNRESOLVED
          default: INCIDENT_STATUS_UNSPECIFIED
        - name: incidentApplication
          in: query
          required: false
          type: string
        - name: incidentNamespace
          in: query
          required: false
          type: string
        - name: titleContains
          in: query
          required: false
          type: string
        - name: offset
          in: query
          required: false
          type: integer
          format: int64
        - name: limit
          in: query
          required: false
          type: integer
          format: int64
        - name: incidentClusterId
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/ai/conversations/{id}:
    get:
      operationId: AimsService_GetAIConversation
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/GetAIConversationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: id
          in: path
          required: true
          type: string
        - name: instanceId
          in: query
          required: false
          type: string
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/audit-logs:
    get:
      operationId: AimsService_ListAuditLogs
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ListAuditLogsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: filters.actorId
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.k8sResource.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.k8sResource.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.k8sResource.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.k8sResource.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.k8sResource.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.k8sResource.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.k8sResource.enabled
          in: query
          required: false
          type: boolean
        - name: filters.k8sResource.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdApplication.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdApplication.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdApplication.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdApplication.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdApplication.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdApplication.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdApplication.enabled
          in: query
          required: false
          type: boolean
        - name: filters.argocdApplication.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdCluster.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdCluster.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdCluster.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdCluster.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdCluster.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdCluster.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdCluster.enabled
          in: query
          required: false
          type: boolean
        - name: filters.argocdCluster.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdInstance.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdInstance.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdInstance.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdInstance.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdInstance.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdInstance.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdInstance.enabled
          in: query
          required: false
          type: boolean
        - name: filters.argocdInstance.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdProject.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdProject.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdProject.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdProject.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdProject.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdProject.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.argocdProject.enabled
          in: query
          required: false
          type: boolean
        - name: filters.argocdProject.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.member.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.member.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.member.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.member.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.member.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.member.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.member.enabled
          in: query
          required: false
          type: boolean
        - name: filters.member.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.organizationInvite.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.organizationInvite.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.organizationInvite.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.organizationInvite.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.organizationInvite.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.organizationInvite.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.organizationInvite.enabled
          in: query
          required: false
          type: boolean
        - name: filters.organizationInvite.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.action
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.actorType
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.startTime
          in: query
          required: false
          type: string
        - name: filters.endTime
          in: query
          required: false
          type: string
        - name: filters.limit
          in: query
          required: false
          type: integer
          format: int64
        - name: filters.offset
          in: query
          required: false
          type: integer
          format: int64
        - name: filters.kargoInstance.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoInstance.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoInstance.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoInstance.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoInstance.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoInstance.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoInstance.enabled
          in: query
          required: false
          type: boolean
        - name: filters.kargoInstance.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoAgent.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoAgent.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoAgent.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoAgent.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoAgent.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoAgent.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoAgent.enabled
          in: query
          required: false
          type: boolean
        - name: filters.kargoAgent.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoPromotion.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoPromotion.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoPromotion.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoPromotion.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoPromotion.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoPromotion.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoPromotion.enabled
          in: query
          required: false
          type: boolean
        - name: filters.kargoPromotion.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoFreight.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoFreight.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoFreight.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoFreight.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoFreight.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoFreight.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.kargoFreight.enabled
          in: query
          required: false
          type: boolean
        - name: filters.kargoFreight.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.customRoles.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.customRoles.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.customRoles.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.customRoles.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.customRoles.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.customRoles.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.customRoles.enabled
          in: query
          required: false
          type: boolean
        - name: filters.customRoles.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.notificationCfg.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.notificationCfg.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.notificationCfg.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.notificationCfg.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.notificationCfg.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.notificationCfg.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.notificationCfg.enabled
          in: query
          required: false
          type: boolean
        - name: filters.notificationCfg.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.apiKeys.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.apiKeys.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.apiKeys.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.apiKeys.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.apiKeys.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.apiKeys.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.apiKeys.enabled
          in: query
          required: false
          type: boolean
        - name: filters.apiKeys.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addons.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addons.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addons.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addons.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addons.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addons.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addons.enabled
          in: query
          required: false
          type: boolean
        - name: filters.addons.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonRepos.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonRepos.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonRepos.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonRepos.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonRepos.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonRepos.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonRepos.enabled
          in: query
          required: false
          type: boolean
        - name: filters.addonRepos.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonMarketplaceInstall.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonMarketplaceInstall.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonMarketplaceInstall.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonMarketplaceInstall.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonMarketplaceInstall.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonMarketplaceInstall.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.addonMarketplaceInstall.enabled
          in: query
          required: false
          type: boolean
        - name: filters.addonMarketplaceInstall.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspace.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspace.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspace.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspace.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspace.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspace.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspace.enabled
          in: query
          required: false
          type: boolean
        - name: filters.workspace.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspaceMember.objectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspaceMember.objectKind
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspaceMember.objectGroup
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspaceMember.objectParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspaceMember.objectParentParentName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspaceMember.objectParentApplicationName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
        - name: filters.workspaceMember.enabled
          in: query
          required: false
          type: boolean
        - name: filters.workspaceMember.objectParentKargoProjectName
          in: query
          required: false
          type: array
          items:
            type: string
          collectionFormat: multi
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/custom-roles:
    get:
      operationId: AimsService_ListOrganizationCustomRoles
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ListOrganizationCustomRolesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: limit
          in: query
          required: false
          type: integer
          format: int64
        - name: offset
          in: query
          required: false
          type: integer
          format: int64
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/domains:
    get:
      operationId: AimsService_ListOrganizationDomains
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.ListOrganizationDomainsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
      tags:
        - AimsService
    post:
      operationId: AimsService_UpdateOrganizationDomains
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/UpdateOrganizationDomainsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UpdateOrganizationDomainsBody'
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/k8s/usage:
    get:
      operationId: AimsService_GetKubeVisionUsage
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.GetKubeVisionUsageResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: startTime
          in: query
          required: false
          type: string
          format: date-time
        - name: endTime
          in: query
          required: false
          type: string
          format: date-time
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/plan:
    post:
      operationId: AimsService_UpdateOrganizationBillingPlan
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/UpdateOrganizationBillingPlanResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UpdateOrganizationBillingPlanBody'
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/teams:
    get:
      operationId: AimsService_ListTeams
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.ListTeamsResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: limit
          in: query
          required: false
          type: integer
          format: int64
        - name: offset
          in: query
          required: false
          type: integer
          format: int64
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/teams/{teamName}/members:
    get:
      operationId: AimsService_ListTeamMembers
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.ListTeamMembersResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: teamName
          in: path
          required: true
          type: string
        - name: limit
          in: query
          required: false
          type: string
          format: int64
        - name: offset
          in: query
          required: false
          type: string
          format: int64
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/trial:
    post:
      operationId: AimsService_UpdateOrganizationTrialExpiration
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/UpdateOrganizationTrialExpirationResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/UpdateOrganizationTrialExpirationBody'
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/users:
    get:
      operationId: AimsService_ListOrganizationUsers
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/ListOrganizationUsersResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/verified:
    post:
      operationId: AimsService_SetManuallyVerified
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/SetManuallyVerifiedResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/SetManuallyVerifiedBody'
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/workspaces:
    get:
      operationId: AimsService_ListWorkspaces
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.ListWorkspacesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: limit
          in: query
          required: false
          type: integer
          format: int64
        - name: offset
          in: query
          required: false
          type: integer
          format: int64
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/workspaces/{workspaceId}:
    get:
      operationId: AimsService_GetWorkspace
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.GetWorkspaceResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: workspaceId
          in: path
          required: true
          type: string
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/workspaces/{workspaceId}/custom-roles:
    get:
      operationId: AimsService_ListWorkspaceCustomRoles
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.ListWorkspaceCustomRolesResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: workspaceId
          in: path
          required: true
          type: string
        - name: limit
          in: query
          required: false
          type: integer
          format: int64
        - name: offset
          in: query
          required: false
          type: integer
          format: int64
      tags:
        - AimsService
  /api/v1/aims/organizations/{organizationId}/workspaces/{workspaceId}/members:
    get:
      operationId: AimsService_ListWorkspaceMembers
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.ListWorkspaceMembersResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      parameters:
        - name: organizationId
          in: path
          required: true
          type: string
        - name: workspaceId
          in: path
          required: true
          type: string
        - name: limit
          in: query
          required: false
          type: integer
          format: int64
        - name: offset
          in: query
          required: false
          type: integer
          format: int64
      tags:
        - AimsService
  /api/v1/aims/plans:
    get:
      operationId: AimsService_ListAvailablePlans
      responses:
        "200":
          description: A successful response.
          schema:
            $ref: '#/definitions/aims.v1.ListAvailablePlansResponse'
        default:
          description: An unexpected error response.
          schema:
            $ref: '#/definitions/rpc.Status'
      tags:
        - AimsService
definitions:
  AIConfig:
    type: object
    properties:
      runbooks:
        type: array
        items:
          type: object
          $ref: '#/definitions/Runbook'
      incidents:
        $ref: '#/definitions/IncidentsConfig'
  AIConversation:
    type: object
    properties:
      id:
        type: string
      title:
        type: string
      createTime:
        type: string
        format: date-time
      lastUpdateTime:
        type: string
        format: date-time
      messages:
        type: array
        items:
          type: object
          $ref: '#/definitions/AIMessage'
      processing:
        type: boolean
      public:
        type: boolean
      ownedByMe:
        type: boolean
      contexts:
        type: array
        items:
          type: object
          $ref: '#/definitions/AIMessageContext'
      processingError:
        type: string
      incident:
        $ref: '#/definitions/Incident'
      feedbacks:
        type: array
        items:
          type: string
      promotionAnalysis:
        $ref: '#/definitions/PromotionAnalysis'
      instanceId:
        type: string
      runbooks:
        type: array
        items:
          type: string
  AIConversationStep:
    type: object
    properties:
      name:
        type: string
      status:
        $ref: '#/definitions/AIConversationStepStatus'
      startTime:
        type: string
        format: date-time
      endTime:
        type: string
        format: date-time
      summary:
        type: string
  AIConversationStepStatus:
    type: string
    enum:
      - AI_CONVERSATION_STEP_STATUS_UNSPECIFIED
      - AI_CONVERSATION_STEP_STATUS_RUNNING
      - AI_CONVERSATION_STEP_STATUS_FAILED
      - AI_CONVERSATION_STEP_STATUS_SUCCEEDED
    default: AI_CONVERSATION_STEP_STATUS_UNSPECIFIED
  AIMessage:
    type: object
    properties:
      createTime:
        type: string
        format: date-time
      role:
        type: string
      content:
        type: string
      thinkingProcess:
        type: string
      suggestedChanges:
        type: array
        items:
          type: object
          $ref: '#/definitions/AISuggestedChange'
      suggestedContexts:
        type: array
        items:
          type: object
          $ref: '#/definitions/AIMessageContext'
      id:
        type: string
      steps:
        type: array
        items:
          type: object
          $ref: '#/definitions/AIConversationStep'
      username:
        type: string
      ownedByMe:
        type: boolean
      isUseful:
        type: boolean
      additionalContent:
        type: array
        items:
          type: object
          $ref: '#/definitions/AIMessageAdditionalContent'
  AIMessageAdditionalContent:
    type: object
    properties:
      type:
        $ref: '#/definitions/AIMessageAdditionalContentType'
        title: The type of additional content
      content:
        type: string
        title: The actual content, e.g., markdown text or diff
      buttonTitle:
        type: string
        title: Optional button for the content
      buttonPrompt:
        type: string
      contentTitle:
        type: string
        title: title for the content, e.g., "Generated Runbook", "Stored Runbook"
  AIMessageAdditionalContentType:
    type: string
    enum:
      - AI_MESSAGE_ADDITIONAL_CONTENT_TYPE_UNSPECIFIED
      - AI_MESSAGE_ADDITIONAL_CONTENT_TYPE_DIFF
      - AI_MESSAGE_ADDITIONAL_CONTENT_TYPE_MARKDOWN
    default: AI_MESSAGE_ADDITIONAL_CONTENT_TYPE_UNSPECIFIED
  AIMessageContext:
    type: object
    properties:
      argoCdApp:
        $ref: '#/definitions/ArgoCDAppContext'
      k8sNamespace:
        $ref: '#/definitions/K8SNamespaceContext'
      kargoProject:
        $ref: '#/definitions/KargoProjectContext'
  AISuggestedChange:
    type: object
    properties:
      context:
        $ref: '#/definitions/AIMessageContext'
      old:
        type: string
      new:
        type: string
      patch:
        type: string
      applied:
        type: boolean
  AIUsage:
    type: object
    properties:
      inputTokens:
        type: integer
        format: int64
      cachedInputTokens:
        type: integer
        format: int64
      outputTokens:
        type: integer
        format: int64
      cost:
        type: number
        format: double
  AgentAggregatedHealthResponse:
    type: object
    properties:
      minObservedGeneration:
        type: string
        format: uint64
      healthy:
        type: object
        additionalProperties:
          $ref: '#/definitions/AgentHealthStatus'
      progressing:
        type: object
        additionalProperties:
          $ref: '#/definitions/AgentHealthStatus'
      degraded:
        type: object
        additionalProperties:
          $ref: '#/definitions/AgentHealthStatus'
      unknown:
        type: object
        additionalProperties:
          $ref: '#/definitions/AgentHealthStatus'
      priorityStatus:
        $ref: '#/definitions/TenantPhase'
  AgentHealthStatus:
    type: object
    properties:
      observedGeneration:
        type: string
        format: uint64
      status:
        $ref: '#/definitions/TenantPhase'
      message:
        type: string
  AgentPermissionsRule:
    type: object
    properties:
      apiGroups:
        type: array
        items:
          type: string
      resources:
        type: array
        items:
          type: string
      verbs:
        type: array
        items:
          type: string
  AgentResources:
    type: object
    properties:
      applicationController:
        $ref: '#/definitions/ApplicationControllerResources'
      repoServer:
        $ref: '#/definitions/RepoServerResources'
  AgentState:
    type: object
    properties:
      version:
        type: string
      argoCdVersion:
        type: string
      observeTime:
        type: string
        format: date-time
      status:
        $ref: '#/definitions/AgentAggregatedHealthResponse'
      agentIds:
        type: array
        items:
          type: string
      lastUserAppliedGeneration:
        type: string
        format: uint64
      agentResources:
        $ref: '#/definitions/AgentResources'
      updateStatus:
        $ref: '#/definitions/AgentUpdateStatus'
  AgentUpdateStatus:
    type: string
    enum:
      - AGENT_UPDATE_STATUS_UNSPECIFIED
      - AGENT_UPDATE_STATUS_UPDATED
      - AGENT_UPDATE_STATUS_IN_PROGRESS
      - AGENT_UPDATE_STATUS_DELAYED
    default: AGENT_UPDATE_STATUS_UNSPECIFIED
  AkuityIntelligence:
    type: object
    properties:
      aiSupportEngineerEnabled:
        type: boolean
      enabled:
        type: boolean
      allowedUsernames:
        type: array
        items:
          type: string
      allowedGroups:
        type: array
        items:
          type: string
      modelVersion:
        type: string
  AkuityIntelligenceExtension:
    type: object
    properties:
      enabled:
        type: boolean
      allowedUsernames:
        type: array
        items:
          type: string
      allowedGroups:
        type: array
        items:
          type: string
      aiSupportEngineerEnabled:
        type: boolean
      modelVersion:
        type: string
  Any:
    type: object
    properties:
      '@type':
        type: string
        description: |-
          A URL/resource name that uniquely identifies the type of the serialized
          protocol buffer message. This string must contain at least
          one "/" character. The last segment of the URL's path must represent
          the fully qualified name of the type (as in
          `path/google.protobuf.Duration`). The name should be in a canonical form
          (e.g., leading "." is not accepted).

          In practice, teams usually precompile into the binary all types that they
          expect it to use in the context of Any. However, for URLs which use the
          scheme `http`, `https`, or no scheme, one can optionally set up a type
          server that maps type URLs to message definitions as follows:

          * If no scheme is provided, `https` is assumed.
          * An HTTP GET on the URL must yield a [google.protobuf.Type][]
            value in binary format, or produce an error.
          * Applications are allowed to cache lookup results based on the
            URL, or have them precompiled into a binary to avoid any
            lookup. Therefore, binary compatibility needs to be preserved
            on changes to types. (Use versioned type names to manage
            breaking changes.)

          Note: this functionality is not currently available in the official
          protobuf release, and it is not used for type URLs beginning with
          type.googleapis.com. As of May 2023, there are no widely used type server
          implementations and no plans to implement one.

          Schemes other than `http`, `https` (or the empty scheme) might be
          used with implementation specific semantics.
    additionalProperties: {}
    description: |-
      `Any` contains an arbitrary serialized protocol buffer message along with a
      URL that describes the type of the serialized message.

      Protobuf library provides support to pack/unpack Any values in the form
      of utility functions or additional generated methods of the Any type.

      Example 1: Pack and unpack a message in C++.

          Foo foo = ...;
          Any any;
          any.PackFrom(foo);
          ...
          if (any.UnpackTo(&foo)) {
            ...
          }

      Example 2: Pack and unpack a message in Java.

          Foo foo = ...;
          Any any = Any.pack(foo);
          ...
          if (any.is(Foo.class)) {
            foo = any.unpack(Foo.class);
          }
          // or ...
          if (any.isSameTypeAs(Foo.getDefaultInstance())) {
            foo = any.unpack(Foo.getDefaultInstance());
          }

       Example 3: Pack and unpack a message in Python.

          foo = Foo(...)
          any = Any()
          any.Pack(foo)
          ...
          if any.Is(Foo.DESCRIPTOR):
            any.Unpack(foo)
            ...

       Example 4: Pack and unpack a message in Go

           foo := &pb.Foo{...}
           any, err := anypb.New(foo)
           if err != nil {
             ...
           }
           ...
           foo := &pb.Foo{}
           if err := any.UnmarshalTo(foo); err != nil {
             ...
           }

      The pack methods provided by protobuf library will by default use
      'type.googleapis.com/full.type.name' as the type URL and the unpack
      methods only use the fully qualified type name after the last '/'
      in the type URL, for example "foo.bar.com/x/y.z" will yield type
      name "y.z".

      JSON
      ====
      The JSON representation of an `Any` value uses the regular
      representation of the deserialized, embedded message, with an
      additional field `@type` which contains the type URL. Example:

          package google.profile;
          message Person {
            string first_name = 1;
            string last_name = 2;
          }

          {
            "@type": "type.googleapis.com/google.profile.Person",
            "firstName": <string>,
            "lastName": <string>
          }

      If the embedded message type is well-known and has a custom JSON
      representation, that representation will be embedded adding a field
      `value` which holds the custom JSON in addition to the `@type`
      field. Example (for message [google.protobuf.Duration][]):

          {
            "@type": "type.googleapis.com/google.protobuf.Duration",
            "value": "1.212s"
          }
  AppControllerAutoScalingConfig:
    type: object
    properties:
      resourceMinimum:
        $ref: '#/definitions/Resources'
      resourceMaximum:
        $ref: '#/definitions/Resources'
  AppInAnyNamespaceConfig:
    type: object
    properties:
      enabled:
        type: boolean
  AppReconciliationsRateLimiting:
    type: object
    properties:
      bucketRateLimiting:
        $ref: '#/definitions/BucketRateLimiting'
      itemRateLimiting:
        $ref: '#/definitions/ItemRateLimiting'
  AppSetDelegate:
    type: object
    properties:
      managedCluster:
        $ref: '#/definitions/ManagedCluster'
  ApplicationControllerResources:
    type: object
    properties:
      requests:
        $ref: '#/definitions/Resources'
      limits:
        $ref: '#/definitions/Resources'
  ApplicationSetExtension:
    type: object
    properties:
      enabled:
        type: boolean
  ApplicationsHealth:
    type: object
    properties:
      healthyCount:
        type: integer
        format: int64
      degradedCount:
        type: integer
        format: int64
      progressingCount:
        type: integer
        format: int64
      unknownCount:
        type: integer
        format: int64
      suspendedCount:
        type: integer
        format: int64
      missingCount:
        type: integer
        format: int64
  ApplicationsStatus:
    type: object
    properties:
      applicationCount:
        type: integer
        format: int64
      resourcesCount:
        type: integer
        format: int64
      syncInProgressCount:
        type: integer
        format: int64
      warningCount:
        type: integer
        format: int64
      errorCount:
        type: integer
        format: int64
      health:
        $ref: '#/definitions/ApplicationsHealth'
      syncStatus:
        $ref: '#/definitions/ApplicationsSyncStatus'
      appOfAppCount:
        type: integer
        format: int64
  ApplicationsSyncStatus:
    type: object
    properties:
      syncedCount:
        type: integer
        format: int64
      outOfSyncCount:
        type: integer
        format: int64
      unknownCount:
        type: integer
        format: int64
  AppsetPlugins:
    type: object
    properties:
      name:
        type: string
      token:
        type: string
      baseUrl:
        type: string
      requestTimeout:
        type: integer
        format: int32
  AppsetPolicy:
    type: object
    properties:
      policy:
        type: string
      overridePolicy:
        type: boolean
  ArgoCDAlertConfig:
    type: object
    properties:
      message:
        type: string
      url:
        type: string
  ArgoCDAppContext:
    type: object
    properties:
      instanceId:
        type: string
      name:
        type: string
  ArgoCDBannerConfig:
    type: object
    properties:
      message:
        type: string
      url:
        type: string
      permanent:
        type: boolean
  ArgoCDConfigMap:
    type: object
    properties:
      adminEnabled:
        type: boolean
      statusBadge:
        $ref: '#/definitions/ArgoCDStatusBadgeConfig'
      googleAnalytics:
        $ref: '#/definitions/ArgoCDGoogleAnalyticsConfig'
      allowAnonymousUser:
        type: boolean
      banner:
        $ref: '#/definitions/ArgoCDBannerConfig'
      chat:
        $ref: '#/definitions/ArgoCDAlertConfig'
      instanceLabelKey:
        type: string
      kustomizeSettings:
        $ref: '#/definitions/ArgoCDKustomizeSettings'
      helmSettings:
        $ref: '#/definitions/ArgoCDHelmSettings'
      resourceSettings:
        $ref: '#/definitions/ArgoCDResourceSettings'
      usersSessionDuration:
        type: string
      oidcConfig:
        type: string
      dexConfig:
        type: string
      webTerminal:
        $ref: '#/definitions/ArgoCDWebTerminalConfig'
      deepLinks:
        $ref: '#/definitions/ArgoCDDeepLinks'
      logsRbacEnabled:
        type: boolean
  ArgoCDDeepLinks:
    type: object
    properties:
      projectLinks:
        type: array
        items:
          type: object
          $ref: '#/definitions/DeepLink'
      applicationLinks:
        type: array
        items:
          type: object
          $ref: '#/definitions/DeepLink'
      resourceLinks:
        type: array
        items:
          type: object
          $ref: '#/definitions/DeepLink'
  ArgoCDExtensionInstallEntry:
    type: object
    properties:
      id:
        type: string
      version:
        type: string
  ArgoCDGoogleAnalyticsConfig:
    type: object
    properties:
      trackingId:
        type: string
      anonymizeUsers:
        type: boolean
  ArgoCDHelmSettings:
    type: object
    properties:
      enabled:
        type: boolean
      valueFileSchemas:
        type: string
  ArgoCDKustomizeSettings:
    type: object
    properties:
      enabled:
        type: boolean
      buildOptions:
        type: string
  ArgoCDRBACConfigMap:
    type: object
    properties:
      defaultPolicy:
        type: string
      policyCsv:
        type: string
      scopes:
        type: array
        items:
          type: string
      overlayPolicies:
        type: array
        items:
          type: object
          $ref: '#/definitions/OverlayPolicy'
  ArgoCDResourceSettings:
    type: object
    properties:
      inclusions:
        type: string
      exclusions:
        type: string
      compareOptions:
        type: string
  ArgoCDStatusBadgeConfig:
    type: object
    properties:
      enabled:
        type: boolean
      url:
        type: string
  ArgoCDWebTerminalConfig:
    type: object
    properties:
      enabled:
        type: boolean
      shells:
        type: string
  ArgoInstanceFilter:
    type: object
    properties:
      paid:
        type: boolean
      unpaid:
        type: boolean
      fuzz:
        type: string
        title: search by instance name/id
      timeFrom:
        type: string
      organizationId:
        type: string
        title: filter by organization ID
  Audit:
    type: object
    properties:
      actor:
        type: string
      reason:
        type: string
  AuditFilters:
    type: object
    properties:
      actorId:
        type: array
        items:
          type: string
      k8sResource:
        $ref: '#/definitions/ObjectFilter'
      argocdApplication:
        $ref: '#/definitions/ObjectFilter'
      argocdCluster:
        $ref: '#/definitions/ObjectFilter'
      argocdInstance:
        $ref: '#/definitions/ObjectFilter'
      argocdProject:
        $ref: '#/definitions/ObjectFilter'
      member:
        $ref: '#/definitions/ObjectFilter'
      organizationInvite:
        $ref: '#/definitions/ObjectFilter'
      action:
        type: array
        items:
          type: string
      actorType:
        type: array
        items:
          type: string
      startTime:
        type: string
      endTime:
        type: string
      limit:
        type: integer
        format: int64
      offset:
        type: integer
        format: int64
      kargoInstance:
        $ref: '#/definitions/ObjectFilter'
      kargoAgent:
        $ref: '#/definitions/ObjectFilter'
      kargoPromotion:
        $ref: '#/definitions/ObjectFilter'
      kargoFreight:
        $ref: '#/definitions/ObjectFilter'
      customRoles:
        $ref: '#/definitions/ObjectFilter'
      notificationCfg:
        $ref: '#/definitions/ObjectFilter'
      apiKeys:
        $ref: '#/definitions/ObjectFilter'
      addons:
        $ref: '#/definitions/ObjectFilter'
      addonRepos:
        $ref: '#/definitions/ObjectFilter'
      addonMarketplaceInstall:
        $ref: '#/definitions/ObjectFilter'
      workspace:
        $ref: '#/definitions/ObjectFilter'
      workspaceMember:
        $ref: '#/definitions/ObjectFilter'
  AuditObjId:
    type: object
    properties:
      name:
        type: string
      kind:
        type: string
      group:
        type: string
  AuditParentId:
    type: object
    properties:
      name:
        type: string
      parentName:
        type: string
      applicationName:
        type: string
  AutoScalerConfig:
    type: object
    properties:
      applicationController:
        $ref: '#/definitions/AppControllerAutoScalingConfig'
      repoServer:
        $ref: '#/definitions/RepoServerAutoScalingConfig'
  BasicOrganization:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      billed:
        type: boolean
      emails:
        type: array
        items:
          type: string
      manuallyVerified:
        type: boolean
      numInstances:
        type: string
        format: uint64
      plan:
        type: string
      status:
        $ref: '#/definitions/OrganizationStatus'
      quota:
        $ref: '#/definitions/OrganizationQuota'
      usage:
        $ref: '#/definitions/OrganizationUsage'
      billingDetails:
        $ref: '#/definitions/BillingDetails'
      stripeData:
        $ref: '#/definitions/StripeData'
      creationTimestamp:
        type: string
        format: date-time
      inactive:
        type: boolean
      canDelete:
        type: boolean
      misc:
        type: object
        additionalProperties:
          type: integer
          format: int32
  BillingDetails:
    type: object
    properties:
      email:
        type: string
      hasActiveSubscription:
        type: boolean
      metadata:
        $ref: '#/definitions/BillingMetadata'
      customerId:
        type: string
      billingAuthority:
        type: string
      manual:
        type: boolean
      lastFourCardDigits:
        type: string
      addons:
        type: array
        items:
          type: object
          $ref: '#/definitions/SubscriptionAddon'
  BillingMetadata:
    type: object
    properties:
      name:
        type: string
  BucketRateLimiting:
    type: object
    properties:
      enabled:
        type: boolean
      bucketSize:
        type: integer
        format: int64
      bucketQps:
        type: integer
        format: int64
  Cluster:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      description:
        type: string
      namespace:
        type: string
        title: Use data.namespace instead
      namespaceScoped:
        type: boolean
        title: Use data.namespace_scoped instead
      data:
        $ref: '#/definitions/ClusterData'
      deleteTime:
        type: string
        format: date-time
      observedGeneration:
        type: string
        format: uint64
      credentialRotationAllowed:
        type: boolean
      agentState:
        $ref: '#/definitions/AgentState'
      healthStatus:
        $ref: '#/definitions/health.v1.Status'
      reconciliationStatus:
        $ref: '#/definitions/reconciliation.v1.Status'
      readonlySettingsChangedGeneration:
        type: string
        format: uint64
      k8sStatus:
        $ref: '#/definitions/ClusterKubernetesStatus'
  ClusterArgoCDNotificationsSettings:
    type: object
    properties:
      inClusterSettings:
        type: boolean
  ClusterCompatibility:
    type: object
    properties:
      ipv6Only:
        type: boolean
  ClusterCustomization:
    type: object
    properties:
      autoUpgradeDisabled:
        type: boolean
      kustomization:
        type: object
      appReplication:
        type: boolean
      redisTunneling:
        type: boolean
  ClusterData:
    type: object
    properties:
      size:
        $ref: '#/definitions/ClusterSize'
      labels:
        type: object
        additionalProperties:
          type: string
      annotations:
        type: object
        additionalProperties:
          type: string
      autoUpgradeDisabled:
        type: boolean
      kustomization:
        type: object
      appReplication:
        type: boolean
      targetVersion:
        type: string
      redisTunneling:
        type: boolean
      directClusterSpec:
        $ref: '#/definitions/DirectClusterSpec'
      datadogAnnotationsEnabled:
        type: boolean
      namespace:
        type: string
      namespaceScoped:
        type: boolean
      eksAddonEnabled:
        type: boolean
      managedClusterConfig:
        $ref: '#/definitions/ManagedClusterConfig'
      maintenanceMode:
        type: boolean
      multiClusterK8sDashboardEnabled:
        type: boolean
      autoscalerConfig:
        $ref: '#/definitions/AutoScalerConfig'
      project:
        type: string
      compatibility:
        $ref: '#/definitions/ClusterCompatibility'
      argocdNotificationsSettings:
        $ref: '#/definitions/ClusterArgoCDNotificationsSettings'
  ClusterKubernetesStatus:
    type: object
    properties:
      kubernetesVersion:
        type: string
      apiResourceCount:
        type: integer
        format: int64
      objectCount:
        type: integer
        format: int64
  ClusterSecretMapping:
    type: object
    properties:
      clusters:
        $ref: '#/definitions/ObjectSelector'
      secrets:
        $ref: '#/definitions/ObjectSelector'
  ClusterSize:
    type: string
    enum:
      - CLUSTER_SIZE_UNSPECIFIED
      - CLUSTER_SIZE_SMALL
      - CLUSTER_SIZE_MEDIUM
      - CLUSTER_SIZE_LARGE
      - CLUSTER_SIZE_AUTO
    default: CLUSTER_SIZE_UNSPECIFIED
  CrossplaneExtension:
    type: object
    properties:
      resources:
        type: array
        items:
          type: object
          $ref: '#/definitions/CrossplaneExtensionResource'
  CrossplaneExtensionResource:
    type: object
    properties:
      group:
        type: string
        title: |-
          supports glob pattern - argocd uses
          [minimatch](https://www.npmjs.com/package/minimatch) package to match group
  CustomDeprecatedAPI:
    type: object
    properties:
      apiVersion:
        type: string
      newApiVersion:
        type: string
      deprecatedInKubernetesVersion:
        type: string
      unavailableInKubernetesVersion:
        type: string
  Customer:
    type: object
    properties:
      stripeId:
        type: string
      organizationId:
        type: string
      billingName:
        type: string
      billingEmail:
        type: string
  CveScanConfig:
    type: object
    properties:
      scanEnabled:
        type: boolean
      rescanInterval:
        type: string
  DataDogRolloutsSecret:
    type: object
    properties:
      address:
        type: string
      apiKey:
        type: string
      appKey:
        type: string
  DecrementInstanceGenerationBody:
    type: object
    properties:
      audit:
        $ref: '#/definitions/Audit'
  DecrementInstanceGenerationResponse:
    type: object
    title: explicitly empty
  DeepLink:
    type: object
    properties:
      title:
        type: string
      url:
        type: string
      description:
        type: string
      iconClass:
        type: string
      if:
        type: string
  DeleteUnpaidInstanceResponse:
    type: object
    title: explicitly empty
  DirectClusterSpec:
    type: object
    properties:
      clusterType:
        $ref: '#/definitions/DirectClusterType'
      kargoInstanceId:
        type: string
      server:
        type: string
      organization:
        type: string
      token:
        type: string
      caData:
        type: string
  DirectClusterType:
    type: string
    enum:
      - DIRECT_CLUSTER_TYPE_UPBOUND
      - DIRECT_CLUSTER_TYPE_KARGO
    default: DIRECT_CLUSTER_TYPE_UPBOUND
    title: '- DIRECT_CLUSTER_TYPE_UPBOUND: buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX'
  DomainVerification:
    type: object
    properties:
      domain:
        type: string
      verified:
        type: boolean
  GetAIConversationResponse:
    type: object
    properties:
      conversation:
        $ref: '#/definitions/AIConversation'
  GetFeatureGatesResponse:
    type: object
    properties:
      featureGates:
        $ref: '#/definitions/OrganizationFeatureGates'
      systemFeatureGates:
        $ref: '#/definitions/SystemFeatureGates'
  GetInstanceByIdResponse:
    type: object
    properties:
      instance:
        $ref: '#/definitions/InternalInstance'
  GetInternalAuditLogsResponse:
    type: object
    properties:
      items:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.AuditLog'
      totalCount:
        type: integer
        format: int64
  GetInternalConfigResponse:
    type: object
    properties:
      config:
        $ref: '#/definitions/InternalConfig'
  GetKargoInstanceByIdResponse:
    type: object
    properties:
      instance:
        $ref: '#/definitions/InternalKargoInstance'
  HostAliases:
    type: object
    properties:
      ip:
        type: string
      hostnames:
        type: array
        items:
          type: string
  HttpBody:
    type: object
    properties:
      contentType:
        type: string
        description: The HTTP Content-Type header value specifying the content type of the body.
      data:
        type: string
        format: byte
        description: The HTTP request/response body as raw binary.
      extensions:
        type: array
        items:
          type: object
          $ref: '#/definitions/Any'
        description: |-
          Application specific response metadata. Must be set in the first response
          for streaming APIs.
    description: |-
      Message that represents an arbitrary HTTP body. It should only be used for
      payload formats that can't be represented as JSON, such as raw binary or
      an HTML page.


      This message can be used both in streaming and non-streaming API methods in
      the request as well as the response.

      It can be used as a top-level request field, which is convenient if one
      wants to extract parameters from either the URL or HTTP template into the
      request fields and also want access to the raw HTTP body.

      Example:

          message GetResourceRequest {
            // A unique request id.
            string request_id = 1;

            // The raw HTTP body is bound to this field.
            google.api.HttpBody http_body = 2;

          }

          service ResourceService {
            rpc GetResource(GetResourceRequest)
              returns (google.api.HttpBody);
            rpc UpdateResource(google.api.HttpBody)
              returns (google.protobuf.Empty);

          }

      Example with streaming methods:

          service CaldavService {
            rpc GetCalendar(stream google.api.HttpBody)
              returns (stream google.api.HttpBody);
            rpc UpdateCalendar(stream google.api.HttpBody)
              returns (stream google.api.HttpBody);

          }

      Use of this type only changes how the request and response bodies are
      handled, all other features will continue to work unchanged.
  IPAllowListEntry:
    type: object
    properties:
      ip:
        type: string
      description:
        type: string
  ImageUpdaterDelegate:
    type: object
    properties:
      controlPlane:
        type: boolean
      managedCluster:
        $ref: '#/definitions/ManagedCluster'
  Incident:
    type: object
    properties:
      resolvedAt:
        type: string
        format: date-time
      summary:
        type: string
      rootCause:
        type: string
      resolution:
        type: string
      runbooks:
        type: array
        items:
          type: string
      application:
        type: string
      namespace:
        type: string
      instanceId:
        type: string
      clusterId:
        type: string
      instanceHostname:
        type: string
  IncidentStatus:
    type: string
    enum:
      - INCIDENT_STATUS_UNSPECIFIED
      - INCIDENT_STATUS_RESOLVED
      - INCIDENT_STATUS_UNRESOLVED
    default: INCIDENT_STATUS_UNSPECIFIED
  IncidentWebhookConfig:
    type: object
    properties:
      name:
        type: string
      descriptionPath:
        type: string
      clusterPath:
        type: string
      k8sNamespacePath:
        type: string
      argocdApplicationNamePath:
        type: string
  IncidentsConfig:
    type: object
    properties:
      triggers:
        type: array
        items:
          type: object
          $ref: '#/definitions/TargetSelector'
      webhooks:
        type: array
        items:
          type: object
          $ref: '#/definitions/IncidentWebhookConfig'
  InfluxDbRolloutsSecret:
    type: object
    properties:
      influxdbAddress:
        type: string
      authToken:
        type: string
      org:
        type: string
  Instance:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      hostname:
        type: string
      clusterCount:
        type: integer
        format: int64
      secrets:
        type: object
        additionalProperties:
          type: string
      generation:
        type: integer
        format: int64
      recentProcessedEventId:
        type: integer
        format: int64
      healthStatus:
        $ref: '#/definitions/health.v1.Status'
      reconciliationStatus:
        $ref: '#/definitions/reconciliation.v1.Status'
      deleteTime:
        type: string
        format: date-time
      ownerOrganizationName:
        type: string
      description:
        type: string
      version:
        type: string
      spec:
        $ref: '#/definitions/InstanceSpec'
      config:
        $ref: '#/definitions/ArgoCDConfigMap'
      rbacConfig:
        $ref: '#/definitions/ArgoCDRBACConfigMap'
      info:
        $ref: '#/definitions/InstanceInfo'
      shard:
        type: string
      workspaceId:
        type: string
      notIntegrationClusterCount:
        type: integer
        format: int64
      unsupportedVersion:
        type: boolean
  InstanceClusterMaintenanceBody:
    type: object
    properties:
      maintanenceMode:
        type: boolean
      audit:
        $ref: '#/definitions/Audit'
  InstanceClusterMaintenanceResponse:
    type: object
    title: explicitly empty
  InstanceInfo:
    type: object
    properties:
      applicationsStatus:
        $ref: '#/definitions/ApplicationsStatus'
      certificateStatus:
        $ref: '#/definitions/argocd.v1.CertificateStatus'
  InstanceSpec:
    type: object
    properties:
      ipAllowList:
        type: array
        items:
          type: object
          $ref: '#/definitions/IPAllowListEntry'
      subdomain:
        type: string
      declarativeManagementEnabled:
        type: boolean
      extensions:
        type: array
        items:
          type: object
          $ref: '#/definitions/ArgoCDExtensionInstallEntry'
      clusterCustomizationDefaults:
        $ref: '#/definitions/ClusterCustomization'
      imageUpdaterEnabled:
        type: boolean
      backendIpAllowListEnabled:
        type: boolean
      repoServerDelegate:
        $ref: '#/definitions/RepoServerDelegate'
      auditExtensionEnabled:
        type: boolean
      syncHistoryExtensionEnabled:
        type: boolean
      crossplaneExtension:
        $ref: '#/definitions/CrossplaneExtension'
      imageUpdaterDelegate:
        $ref: '#/definitions/ImageUpdaterDelegate'
      appSetDelegate:
        $ref: '#/definitions/AppSetDelegate'
      assistantExtensionEnabled:
        type: boolean
      appsetPolicy:
        $ref: '#/definitions/AppsetPolicy'
      hostAliases:
        type: array
        items:
          type: object
          $ref: '#/definitions/HostAliases'
      agentPermissionsRules:
        type: array
        items:
          type: object
          $ref: '#/definitions/AgentPermissionsRule'
      fqdn:
        type: string
      multiClusterK8sDashboardEnabled:
        type: boolean
      akuityIntelligenceExtension:
        $ref: '#/definitions/AkuityIntelligenceExtension'
      imageUpdaterVersion:
        type: string
      customDeprecatedApis:
        type: array
        items:
          type: object
          $ref: '#/definitions/CustomDeprecatedAPI'
      kubeVisionConfig:
        $ref: '#/definitions/KubeVisionConfig'
      appInAnyNamespaceConfig:
        $ref: '#/definitions/AppInAnyNamespaceConfig'
      basepath:
        type: string
      appsetProgressiveSyncsEnabled:
        type: boolean
      secrets:
        $ref: '#/definitions/SecretsManagementConfig'
      appsetPlugins:
        type: array
        items:
          type: object
          $ref: '#/definitions/AppsetPlugins'
      applicationSetExtension:
        $ref: '#/definitions/ApplicationSetExtension'
      appReconciliationsRateLimiting:
        $ref: '#/definitions/AppReconciliationsRateLimiting'
  InternalAuditFilters:
    type: object
    properties:
      actorId:
        type: array
        items:
          type: string
      objectType:
        type: string
      action:
        type: array
        items:
          type: string
      startTime:
        type: string
      endTime:
        type: string
      limit:
        type: integer
        format: int64
      offset:
        type: integer
        format: int64
  InternalConfig:
    type: object
    properties:
      disableFreeInstanceCreation:
        type: boolean
  InternalInstance:
    type: object
    properties:
      instance:
        $ref: '#/definitions/Instance'
      createTime:
        type: string
        format: date-time
      expired:
        type: boolean
      connectedClusters:
        type: integer
        format: int64
      statusProcessedInfo:
        type: string
      orgId:
        type: string
      notificationConfig:
        $ref: '#/definitions/aims.v1.NotificationConfig'
      workspace:
        $ref: '#/definitions/aims.v1.Workspace'
  InternalKargoInstance:
    type: object
    properties:
      instance:
        $ref: '#/definitions/KargoInstance'
      organization:
        $ref: '#/definitions/BasicOrganization'
        title: only basic fields of BasicOrganization
      creationTimestamp:
        type: string
        format: date-time
      workspace:
        $ref: '#/definitions/aims.v1.Workspace'
      statusInfo:
        type: object
  ItemRateLimiting:
    type: object
    properties:
      enabled:
        type: boolean
      failureCooldown:
        type: integer
        format: int64
      baseDelay:
        type: integer
        format: int64
      maxDelay:
        type: integer
        format: int64
      backoffFactor:
        type: number
        format: float
  K8SNamespaceContext:
    type: object
    properties:
      instanceId:
        type: string
      clusterId:
        type: string
      name:
        type: string
  KargoAgent:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      namespace:
        type: string
        title: Use data.namespace instead
      description:
        type: string
      data:
        $ref: '#/definitions/KargoAgentData'
      deleteTime:
        type: string
        format: date-time
      healthStatus:
        $ref: '#/definitions/health.v1.Status'
      reconciliationStatus:
        $ref: '#/definitions/reconciliation.v1.Status'
      agentState:
        $ref: '#/definitions/KargoAgentState'
      readonlySettingsChangedGeneration:
        type: string
        format: uint64
      observedGeneration:
        type: string
        format: uint64
  KargoAgentCustomization:
    type: object
    properties:
      autoUpgradeDisabled:
        type: boolean
      kustomization:
        type: object
  KargoAgentData:
    type: object
    properties:
      size:
        $ref: '#/definitions/KargoAgentSize'
      labels:
        type: object
        additionalProperties:
          type: string
      annotations:
        type: object
        additionalProperties:
          type: string
      autoUpgradeDisabled:
        type: boolean
      targetVersion:
        type: string
      kustomization:
        type: object
      remoteArgocd:
        type: string
      akuityManaged:
        type: boolean
      namespace:
        type: string
      argocdNamespace:
        type: string
      selfManagedArgocdUrl:
        type: string
  KargoAgentSize:
    type: string
    enum:
      - KARGO_AGENT_SIZE_UNSPECIFIED
      - KARGO_AGENT_SIZE_SMALL
      - KARGO_AGENT_SIZE_MEDIUM
      - KARGO_AGENT_SIZE_LARGE
    default: KARGO_AGENT_SIZE_UNSPECIFIED
  KargoAgentState:
    type: object
    properties:
      version:
        type: string
      kargoVersion:
        type: string
      observeTime:
        type: string
        format: date-time
      status:
        $ref: '#/definitions/AgentAggregatedHealthResponse'
      agentIds:
        type: array
        items:
          type: string
      lastUserAppliedGeneration:
        type: string
        format: uint64
      updateStatus:
        $ref: '#/definitions/AgentUpdateStatus'
  KargoApiCM:
    type: object
    properties:
      adminAccountEnabled:
        type: boolean
      adminAccountTokenTtl:
        type: string
  KargoApiSecret:
    type: object
    properties:
      adminAccountPasswordHash:
        type: string
  KargoControllerCM:
    type: object
    title: nothing to store for now
  KargoIPAllowListEntry:
    type: object
    properties:
      ip:
        type: string
      description:
        type: string
  KargoInstance:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      description:
        type: string
      spec:
        $ref: '#/definitions/KargoInstanceSpec'
      hostname:
        type: string
      generation:
        type: integer
        format: int64
      healthStatus:
        $ref: '#/definitions/health.v1.Status'
      reconciliationStatus:
        $ref: '#/definitions/reconciliation.v1.Status'
      deleteTime:
        type: string
        format: date-time
      ownerOrganizationName:
        type: string
      version:
        type: string
      controllerCm:
        $ref: '#/definitions/KargoControllerCM'
      webhookCm:
        $ref: '#/definitions/KargoWebhookCM'
      apiCm:
        $ref: '#/definitions/KargoApiCM'
      apiSecret:
        $ref: '#/definitions/KargoApiSecret'
      oidcConfig:
        $ref: '#/definitions/KargoOidcConfig'
      subdomain:
        type: string
      workspaceId:
        type: string
      miscellaneousSecrets:
        $ref: '#/definitions/KargoMiscellaneousSecrets'
      fqdn:
        type: string
      certificateStatus:
        $ref: '#/definitions/kargo.v1.CertificateStatus'
      unsupportedVersion:
        type: boolean
  KargoInstanceFilter:
    type: object
    properties:
      paid:
        type: boolean
      unpaid:
        type: boolean
      fuzz:
        type: string
        title: search by instance name/id
      timeFrom:
        type: string
      organizationId:
        type: string
        title: filter by organization ID
  KargoInstanceSpec:
    type: object
    properties:
      backendIpAllowListEnabled:
        type: boolean
      ipAllowList:
        type: array
        items:
          type: object
          $ref: '#/definitions/KargoIPAllowListEntry'
      agentCustomizationDefaults:
        $ref: '#/definitions/KargoAgentCustomization'
      defaultShardAgent:
        type: string
      globalCredentialsNs:
        type: array
        items:
          type: string
      globalServiceAccountNs:
        type: array
        items:
          type: string
      akuityIntelligence:
        $ref: '#/definitions/AkuityIntelligence'
  KargoMiscellaneousSecrets:
    type: object
    properties:
      datadogRolloutsSecret:
        $ref: '#/definitions/DataDogRolloutsSecret'
      newrelicRolloutsSecret:
        $ref: '#/definitions/NewRelicRolloutsSecret'
      influxdbRolloutsSecret:
        $ref: '#/definitions/InfluxDbRolloutsSecret'
  KargoOidcConfig:
    type: object
    properties:
      enabled:
        type: boolean
      dexEnabled:
        type: boolean
      dexConfig:
        type: string
      dexConfigSecret:
        type: object
        additionalProperties:
          $ref: '#/definitions/KargoOidcConfig.Value'
      issuerUrl:
        type: string
      clientId:
        type: string
      cliClientId:
        type: string
      adminAccount:
        $ref: '#/definitions/KargoPredefinedAccountData'
      viewerAccount:
        $ref: '#/definitions/KargoPredefinedAccountData'
      additionalScopes:
        type: array
        items:
          type: string
      userAccount:
        $ref: '#/definitions/KargoPredefinedAccountData'
  KargoOidcConfig.Value:
    type: object
    properties:
      value:
        type: string
  KargoPredefinedAccountClaimValue:
    type: object
    properties:
      values:
        type: array
        items:
          type: string
  KargoPredefinedAccountData:
    type: object
    properties:
      email:
        type: array
        items:
          type: string
        title: Use claims instead;
      sub:
        type: array
        items:
          type: string
        title: Use claims instead;
      groups:
        type: array
        items:
          type: string
        title: Use claims instead;
      claims:
        type: object
        additionalProperties:
          $ref: '#/definitions/KargoPredefinedAccountClaimValue'
  KargoProjectContext:
    type: object
    properties:
      instanceId:
        type: string
      name:
        type: string
  KargoWebhookCM:
    type: object
    title: nothing to store for now
  KubeVisionConfig:
    type: object
    properties:
      cveScanConfig:
        $ref: '#/definitions/CveScanConfig'
      aiConfig:
        $ref: '#/definitions/AIConfig'
  KubeVisionUsage:
    type: object
    properties:
      timestamp:
        type: string
        format: date-time
      instanceCount:
        type: integer
        format: int64
      clusterCount:
        type: integer
        format: int64
      apiResourceCount:
        type: integer
        format: int64
      objectCount:
        type: integer
        format: int64
      nodeCount:
        type: integer
        format: int64
      podCount:
        type: integer
        format: int64
      containerCount:
        type: integer
        format: int64
      aiUsage:
        $ref: '#/definitions/AIUsage'
  LabelSelectorRequirement:
    type: object
    properties:
      key:
        type: string
        description: key is the label key that the selector applies to.
      operator:
        type: string
        description: |-
          operator represents a key's relationship to a set of values.
          Valid operators are In, NotIn, Exists and DoesNotExist.
      values:
        type: array
        items:
          type: string
        description: |-
          values is an array of string values. If the operator is In or NotIn,
          the values array must be non-empty. If the operator is Exists or DoesNotExist,
          the values array must be empty. This array is replaced during a strategic
          merge patch.
    description: |-
      A label selector requirement is a selector that contains values, a key, and an operator that
      relates the key and values.
  ListAIConversationsResponse:
    type: object
    properties:
      conversations:
        type: array
        items:
          type: object
          $ref: '#/definitions/AIConversation'
      count:
        type: integer
        format: int64
  ListAgentsForKargoInstanceResponse:
    type: object
    properties:
      agents:
        type: array
        items:
          type: object
          $ref: '#/definitions/KargoAgent'
  ListAllOrganizationsResponse:
    type: object
    properties:
      organizations:
        type: array
        items:
          type: object
          $ref: '#/definitions/BasicOrganization'
      count:
        type: integer
        format: int64
  ListArgoInstancesResponse:
    type: object
    properties:
      instances:
        type: array
        items:
          type: object
          $ref: '#/definitions/InternalInstance'
  ListAuditLogsResponse:
    type: object
    properties:
      auditLogs:
        type: array
        items:
          type: object
          $ref: '#/definitions/organization.v1.AuditLog'
      count:
        type: integer
        format: int64
  ListClustersForInstanceResponse:
    type: object
    properties:
      clusters:
        type: array
        items:
          type: object
          $ref: '#/definitions/Cluster'
  ListOrganizationCustomRolesResponse:
    type: object
    properties:
      customRoles:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.CustomRole'
      totalCount:
        type: string
        format: int64
  ListOrganizationUsersResponse:
    type: object
    properties:
      users:
        type: array
        items:
          type: object
          $ref: '#/definitions/OrganizationUser'
  ListUnbilledOrganizationsResponse:
    type: object
    properties:
      organizations:
        type: array
        items:
          type: object
          $ref: '#/definitions/BasicOrganization'
  ManagedCluster:
    type: object
    properties:
      clusterName:
        type: string
  ManagedClusterConfig:
    type: object
    properties:
      secretName:
        type: string
      secretKey:
        type: string
  NewRelicRolloutsSecret:
    type: object
    properties:
      personalApiKey:
        type: string
      accountId:
        type: string
      region:
        type: string
      baseUrlRest:
        type: string
      baseUrlNerdgraph:
        type: string
  NotificationCategory:
    type: string
    enum:
      - NOTIFICATION_CATEGORY_UNSPECIFIED
      - NOTIFICATION_CATEGORY_CUSTOM
      - NOTIFICATION_CATEGORY_NEW_FEATURE
    default: NOTIFICATION_CATEGORY_UNSPECIFIED
  NullValue:
    type: string
    enum:
      - NULL_VALUE
    default: NULL_VALUE
    description: |-
      `NullValue` is a singleton enumeration to represent the null value for the
      `Value` type union.

      The JSON representation for `NullValue` is JSON `null`.

       - NULL_VALUE: Null value.
  ObjectFilter:
    type: object
    properties:
      objectName:
        type: array
        items:
          type: string
      objectKind:
        type: array
        items:
          type: string
      objectGroup:
        type: array
        items:
          type: string
      objectParentName:
        type: array
        items:
          type: string
      objectParentParentName:
        type: array
        items:
          type: string
      objectParentApplicationName:
        type: array
        items:
          type: string
      enabled:
        type: boolean
      objectParentKargoProjectName:
        type: array
        items:
          type: string
  ObjectSelector:
    type: object
    properties:
      matchLabels:
        type: object
        additionalProperties:
          type: string
        title: |-
          matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
          map is equivalent to an element of matchExpressions, whose key field is "key", the
          operator is "In", and the values array contains only "value". The requirements are ANDed.
          +optional
      matchExpressions:
        type: array
        items:
          type: object
          $ref: '#/definitions/LabelSelectorRequirement'
        description: matchExpressions is a list of label selector requirements. The requirements are ANDed.
  OnboardManualCustomerRequest:
    type: object
    properties:
      customer:
        $ref: '#/definitions/Customer'
      audit:
        $ref: '#/definitions/Audit'
  OnboardManualCustomerResponse:
    type: object
    title: explicitly empty
  OrganizationFeatureGates:
    type: object
    properties:
      sso:
        type: boolean
      shards:
        type: array
        items:
          type: string
      kargo:
        type: boolean
      k3sProxyInformers:
        type: boolean
      team:
        type: boolean
      auditRecordExport:
        type: boolean
      workspaces:
        type: boolean
      customRoles:
        type: boolean
      scopedApiKeys:
        type: boolean
      argocdSso:
        type: boolean
      argocdHaControlPlane:
        type: boolean
      akuityArgocdExtensions:
        type: boolean
      appOfApps:
        type: boolean
      applicationSetController:
        type: boolean
      argocdCustomSubdomain:
        type: boolean
      argocdCustomDomain:
        type: boolean
      argocdFlexibleArchitecture:
        type: boolean
      argocdAgentStateReplication:
        type: boolean
      argocdDeepLinks:
        type: boolean
      argocdCustomStyles:
        type: boolean
      configManagementPlugins:
        type: boolean
      auditArchive:
        type: boolean
      multiClusterK8sDashboard:
        type: boolean
      argocdClusterIntegration:
        type: boolean
      notification:
        type: boolean
      clusterAutoscaler:
        type: boolean
      fleetManagement:
        type: boolean
      pgpool:
        type: boolean
      aiSupportEngineer:
        type: boolean
      pgbouncer:
        type: boolean
      multiFactorAuth:
        type: boolean
      kargoAnalysisLogs:
        type: boolean
      kargoEnterprise:
        type: boolean
      oidcMap:
        type: boolean
      eksAddon:
        type: boolean
      k3sTrafficReduction:
        type: boolean
      redisTrafficReduction:
        type: boolean
  OrganizationFilter:
    type: object
    properties:
      limit:
        type: integer
        format: int64
        title: default limit 50
      offset:
        type: integer
        format: int64
      fuzz:
        type: string
        title: |-
          fuzzy search
          by name
          by id
          by member
      sortByCreation:
        $ref: '#/definitions/Sort'
      billed:
        type: boolean
      manuallyVerified:
        type: boolean
      plans:
        type: array
        items:
          type: string
      startTime:
        type: string
      endTime:
        type: string
  OrganizationMembers:
    type: object
    properties:
      email:
        type: array
        items:
          type: string
  OrganizationQuota:
    type: object
    properties:
      maxInstances:
        type: string
        format: int64
      maxClusters:
        type: string
        format: int64
      maxApplications:
        type: string
        format: int64
      maxKargoInstances:
        type: string
        format: int64
      maxKargoProjects:
        type: string
        format: int64
        title: 'Deprecated: we use stages now'
      maxKargoAgents:
        type: string
        format: int64
      auditRecordMonths:
        type: string
        format: int64
      auditRecordArchiveMonths:
        type: string
        format: int64
      maxOrgMembers:
        type: string
        format: int64
      maxWorkspaces:
        type: string
        format: int64
      maxKargoStages:
        type: string
        format: int64
      maxAiCostPerMonth:
        type: number
        format: double
    description: |-
      OrganizationQuota is a quota for the given organization.
      NOTE: It is encouraged to define quota fields in `double` or `int64` to
      avoid type cast failure.
  OrganizationStatus:
    type: object
    properties:
      state:
        type: string
      trial:
        type: boolean
      expiry:
        type: string
        format: uint64
      billingUpdating:
        type: boolean
  OrganizationUsage:
    type: object
    properties:
      currentInstances:
        type: string
        format: int64
      currentClusters:
        type: string
        format: int64
      currentApplications:
        type: string
        format: int64
      currentKargoInstances:
        type: string
        format: int64
      currentKargoProjects:
        type: string
        format: int64
        title: 'Deprecated: we use stages now'
      currentKargoAgents:
        type: string
        format: int64
      currentOrgMembers:
        type: string
        format: int64
      currentWorkspaces:
        type: string
        format: int64
      currentKargoStages:
        type: string
        format: int64
      currentAiInputTokensPerMonth:
        type: string
        format: int64
      currentAiInputCachedTokensPerMonth:
        type: string
        format: int64
      currentAiOutputTokensPerMonth:
        type: string
        format: int64
      currentAiCostPerMonth:
        type: number
        format: double
    description: OrganizationUsage is the usage of resources for the given organization.
  OrganizationUser:
    type: object
    properties:
      id:
        type: string
      email:
        type: string
      role:
        type: string
      workspaces:
        type: array
        items:
          type: object
          $ref: '#/definitions/WorkspaceInfo'
      teams:
        type: array
        items:
          type: object
          $ref: '#/definitions/TeamInfo'
  OverlayPolicy:
    type: object
    properties:
      name:
        type: string
      policy:
        type: string
  PatchFeatureGatesBody:
    type: object
    properties:
      featureGates:
        $ref: '#/definitions/OrganizationFeatureGates'
      audit:
        $ref: '#/definitions/Audit'
  PatchFeatureGatesResponse:
    type: object
    properties:
      featureGates:
        $ref: '#/definitions/OrganizationFeatureGates'
  PromotionAnalysis:
    type: object
    properties:
      finishTime:
        type: string
        format: date-time
      project:
        type: string
      instanceId:
        type: string
      freight:
        type: string
      stage:
        type: string
      summary:
        type: string
      riskLevel:
        type: string
      decision:
        type: string
      commitDiffUrl:
        type: string
      currentFreight:
        type: string
  RepoServerAutoScalingConfig:
    type: object
    properties:
      resourceMinimum:
        $ref: '#/definitions/Resources'
      resourceMaximum:
        $ref: '#/definitions/Resources'
      replicaMaximum:
        type: integer
        format: int32
      replicaMinimum:
        type: integer
        format: int32
  RepoServerDelegate:
    type: object
    properties:
      controlPlane:
        type: boolean
      managedCluster:
        $ref: '#/definitions/ManagedCluster'
  RepoServerResources:
    type: object
    properties:
      requests:
        $ref: '#/definitions/Resources'
      limits:
        $ref: '#/definitions/Resources'
      replicas:
        type: integer
        format: int64
  ResetMFARequest:
    type: object
    properties:
      organizationId:
        type: string
      email:
        type: string
      audit:
        $ref: '#/definitions/Audit'
  ResetMFAResponse:
    type: object
    title: explicitly empty
  Resources:
    type: object
    properties:
      mem:
        type: string
      cpu:
        type: string
  Runbook:
    type: object
    properties:
      name:
        type: string
      content:
        type: string
      appliedTo:
        $ref: '#/definitions/TargetSelector'
  SecretsManagementConfig:
    type: object
    properties:
      sources:
        type: array
        items:
          type: object
          $ref: '#/definitions/ClusterSecretMapping'
      destinations:
        type: array
        items:
          type: object
          $ref: '#/definitions/ClusterSecretMapping'
  SendNotificationRequest:
    type: object
    properties:
      testMode:
        type: boolean
        title: |-
          test mode ensures that notification
          1. is only sent to the organization containing akuity member
          2. member in the audit email should be same in that organization
      organizationId:
        type: string
        title: currently, only used when test mode is on
      audit:
        $ref: '#/definitions/Audit'
      category:
        $ref: '#/definitions/NotificationCategory'
      metadata:
        type: object
        title: this should match
  SendNotificationResponse:
    type: object
    title: explicitly empty
  SetDisabledInstanceCreationRequest:
    type: object
    properties:
      disabled:
        type: boolean
      audit:
        $ref: '#/definitions/Audit'
  SetDisabledInstanceCreationResponse:
    type: object
    title: explicitly empty
  SetManuallyVerifiedBody:
    type: object
    properties:
      verified:
        type: boolean
      audit:
        $ref: '#/definitions/Audit'
  SetManuallyVerifiedResponse:
    type: object
    title: explicitly empty
  Sort:
    type: string
    enum:
      - SORT_UNSPECIFIED
      - SORT_ASCENDING
      - SORT_DESCENDING
    default: SORT_UNSPECIFIED
  StripeData:
    type: object
    properties:
      subscriptionEndTime:
        type: string
        format: date-time
      stale:
        type: boolean
  SubscriptionAddon:
    type: object
    properties:
      name:
        type: string
      quantity:
        type: integer
        format: int64
      includedQuantity:
        type: integer
        format: int64
      displayName:
        type: string
      unitPrice:
        type: integer
        format: int64
      minimumQuantity:
        type: integer
        format: int64
      maximumQuantity:
        type: integer
        format: int64
      description:
        type: string
  SystemFeatureGates:
    type: object
    properties:
      sso:
        type: boolean
      kargo:
        type: boolean
      autoscaler:
        type: boolean
      k3sProxyInformers:
        type: boolean
      aiAssistantStats:
        type: boolean
      agentPermissions:
        type: boolean
      team:
        type: boolean
      selfServeCancel:
        type: boolean
      k3sCertCnReset:
        type: boolean
      notification:
        type: boolean
      multiClusterK8sDashboard:
        type: boolean
      clusterAutoscaler:
        type: boolean
      fleetManagement:
        type: boolean
      aiSupportEngineer:
        type: boolean
      secretManagement:
        type: boolean
      kargoEnterprise:
        type: boolean
      akiPermissionModel:
        type: boolean
  TargetSelector:
    type: object
    properties:
      argocdApplications:
        type: array
        items:
          type: string
      k8sNamespaces:
        type: array
        items:
          type: string
      clusters:
        type: array
        items:
          type: string
  TeamInfo:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
  TenantPhase:
    type: string
    enum:
      - TENANT_PHASE_UNSPECIFIED
      - TENANT_PHASE_HEALTHY
      - TENANT_PHASE_PROGRESSING
      - TENANT_PHASE_DEGRADED
      - TENANT_PHASE_UNKNOWN
    default: TENANT_PHASE_UNSPECIFIED
  UpdateOrganizationBillingPlanBody:
    type: object
    properties:
      plan:
        type: string
      audit:
        $ref: '#/definitions/Audit'
  UpdateOrganizationBillingPlanResponse:
    type: object
    title: explicitly empty
  UpdateOrganizationDomainsBody:
    type: object
    properties:
      domains:
        type: array
        items:
          type: object
          $ref: '#/definitions/DomainVerification'
      audit:
        $ref: '#/definitions/Audit'
  UpdateOrganizationDomainsResponse:
    type: object
    properties:
      domains:
        type: array
        items:
          type: object
          $ref: '#/definitions/DomainVerification'
  UpdateOrganizationTrialExpirationBody:
    type: object
    properties:
      trialExpiration:
        type: string
        format: uint64
      audit:
        $ref: '#/definitions/Audit'
  UpdateOrganizationTrialExpirationResponse:
    type: object
    title: explicitly empty
  UpdateQuotasBody:
    type: object
    properties:
      audit:
        $ref: '#/definitions/Audit'
      quota:
        $ref: '#/definitions/OrganizationQuota'
  UpdateQuotasResponse:
    type: object
    properties:
      quota:
        $ref: '#/definitions/OrganizationQuota'
      creationTimestamp:
        type: string
        format: date-time
  WorkspaceInfo:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
  aims.v1.AuditLog:
    type: object
    properties:
      timestamp:
        type: string
      action:
        type: string
      actor:
        $ref: '#/definitions/aims.v1.AuditLog.AuditActor'
      object:
        $ref: '#/definitions/aims.v1.AuditLog.AuditObject'
      details:
        $ref: '#/definitions/aims.v1.AuditLog.AuditDetails'
  aims.v1.AuditLog.AuditActor:
    type: object
    properties:
      type:
        type: string
      id:
        type: string
      ip:
        type: string
  aims.v1.AuditLog.AuditDetails:
    type: object
    properties:
      message:
        type: string
      patch:
        type: string
  aims.v1.AuditLog.AuditObject:
    type: object
    properties:
      type:
        type: string
      id:
        type: string
  aims.v1.ClusterFilter:
    type: object
    properties:
      fuzz:
        type: string
        title: search by name/id/namespace
      timeFrom:
        type: string
  aims.v1.CustomRole:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      description:
        type: string
      policy:
        type: string
  aims.v1.DeleteOrganizationResponse:
    type: object
    title: explicitly empty
  aims.v1.GetKubeVisionUsageResponse:
    type: object
    properties:
      usage:
        type: array
        items:
          type: object
          $ref: '#/definitions/KubeVisionUsage'
  aims.v1.GetOrganizationResponse:
    type: object
    properties:
      organization:
        $ref: '#/definitions/BasicOrganization'
  aims.v1.GetWorkspaceResponse:
    type: object
    properties:
      workspace:
        $ref: '#/definitions/aims.v1.Workspace'
  aims.v1.ListAvailablePlansResponse:
    type: object
    properties:
      plans:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.Plan'
  aims.v1.ListKargoInstancesResponse:
    type: object
    properties:
      instances:
        type: array
        items:
          type: object
          $ref: '#/definitions/InternalKargoInstance'
  aims.v1.ListOrganizationDomainsResponse:
    type: object
    properties:
      domains:
        type: array
        items:
          type: object
          $ref: '#/definitions/DomainVerification'
  aims.v1.ListOrganizationMembersResponse:
    type: object
    properties:
      members:
        type: object
        additionalProperties:
          $ref: '#/definitions/OrganizationMembers'
  aims.v1.ListTeamMembersResponse:
    type: object
    properties:
      teamMembers:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.TeamMember'
      count:
        type: string
        format: int64
  aims.v1.ListTeamsResponse:
    type: object
    properties:
      teams:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.Team'
      count:
        type: integer
        format: int64
  aims.v1.ListWorkspaceCustomRolesResponse:
    type: object
    properties:
      customRoles:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.CustomRole'
      workspaceId:
        type: string
      totalCount:
        type: string
        format: int64
  aims.v1.ListWorkspaceMembersResponse:
    type: object
    properties:
      workspaceMembers:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.WorkspaceMember'
      teamMemberCount:
        type: integer
        format: int64
      userMemberCount:
        type: integer
        format: int64
  aims.v1.ListWorkspacesResponse:
    type: object
    properties:
      workspaces:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.Workspace'
      count:
        type: integer
        format: int64
  aims.v1.NotificationConfig:
    type: object
    properties:
      config:
        type: object
        additionalProperties:
          type: string
  aims.v1.Plan:
    type: object
    properties:
      name:
        type: string
      productId:
        type: string
      features:
        type: object
      quotas:
        type: object
      default:
        type: boolean
  aims.v1.Team:
    type: object
    properties:
      name:
        type: string
      description:
        type: string
      createTime:
        type: string
        format: date-time
      memberCount:
        type: string
        format: int64
  aims.v1.TeamMember:
    type: object
    properties:
      id:
        type: string
      email:
        type: string
  aims.v1.Workspace:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      description:
        type: string
      createTime:
        type: string
        format: date-time
      argocdInstances:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.WorkspaceArgoCDInstance'
      kargoInstances:
        type: array
        items:
          type: object
          $ref: '#/definitions/aims.v1.WorkspaceKargoInstance'
      teamMemberCount:
        type: integer
        format: int64
      userMemberCount:
        type: integer
        format: int64
      isDefault:
        type: boolean
  aims.v1.WorkspaceArgoCDInstance:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
  aims.v1.WorkspaceKargoInstance:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
  aims.v1.WorkspaceMember:
    type: object
    properties:
      id:
        type: string
      role:
        $ref: '#/definitions/aims.v1.WorkspaceMemberRole'
      user:
        $ref: '#/definitions/aims.v1.WorkspaceUserMember'
      team:
        $ref: '#/definitions/aims.v1.WorkspaceTeamMember'
  aims.v1.WorkspaceMemberRole:
    type: string
    enum:
      - WORKSPACE_MEMBER_ROLE_UNSPECIFIED
      - WORKSPACE_MEMBER_ROLE_MEMBER
      - WORKSPACE_MEMBER_ROLE_ADMIN
    default: WORKSPACE_MEMBER_ROLE_UNSPECIFIED
  aims.v1.WorkspaceTeamMember:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      description:
        type: string
      createTime:
        type: string
        format: date-time
      memberCount:
        type: string
        format: int64
  aims.v1.WorkspaceUserMember:
    type: object
    properties:
      id:
        type: string
      email:
        type: string
  argocd.v1.CertificateStatus:
    type: object
    properties:
      isCnameSet:
        type: boolean
      isIssued:
        type: boolean
      message:
        type: string
  health.v1.Status:
    type: object
    properties:
      code:
        $ref: '#/definitions/health.v1.StatusCode'
      message:
        type: string
  health.v1.StatusCode:
    type: string
    enum:
      - STATUS_CODE_UNSPECIFIED
      - STATUS_CODE_HEALTHY
      - STATUS_CODE_PROGRESSING
      - STATUS_CODE_DEGRADED
      - STATUS_CODE_UNKNOWN
    default: STATUS_CODE_UNSPECIFIED
  kargo.v1.CertificateStatus:
    type: object
    properties:
      isCnameSet:
        type: boolean
      isIssued:
        type: boolean
      message:
        type: string
  organization.v1.AuditLog:
    type: object
    properties:
      timestamp:
        type: string
      action:
        type: string
      actor:
        $ref: '#/definitions/organization.v1.AuditLog.AuditActor'
      object:
        $ref: '#/definitions/organization.v1.AuditLog.AuditObject'
      details:
        $ref: '#/definitions/organization.v1.AuditLog.AuditDetails'
      count:
        type: integer
        format: int64
      lastOccurredTimestamp:
        type: string
  organization.v1.AuditLog.AuditActor:
    type: object
    properties:
      type:
        type: string
      id:
        type: string
      ip:
        type: string
  organization.v1.AuditLog.AuditDetails:
    type: object
    properties:
      message:
        type: string
      patch:
        type: string
      actionType:
        type: string
  organization.v1.AuditLog.AuditObject:
    type: object
    properties:
      type:
        type: string
      id:
        $ref: '#/definitions/AuditObjId'
      parentId:
        $ref: '#/definitions/AuditParentId'
  reconciliation.v1.Status:
    type: object
    properties:
      code:
        $ref: '#/definitions/reconciliation.v1.StatusCode'
      message:
        type: string
  reconciliation.v1.StatusCode:
    type: string
    enum:
      - STATUS_CODE_UNSPECIFIED
      - STATUS_CODE_SUCCESSFUL
      - STATUS_CODE_PROGRESSING
      - STATUS_CODE_FAILED
    default: STATUS_CODE_UNSPECIFIED
  rpc.Status:
    type: object
    properties:
      code:
        type: integer
        format: int32
      message:
        type: string
      details:
        type: array
        items:
          type: object
          $ref: '#/definitions/Any'
