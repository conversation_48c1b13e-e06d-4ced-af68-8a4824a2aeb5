package argocd

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"

	"github.com/go-resty/resty/v2"
	"google.golang.org/grpc/status"
	"gopkg.in/yaml.v3"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"

	"github.com/akuityio/akuity-platform/internal/utils/errors"
	httputil "github.com/akuityio/akuity-platform/pkg/utils/http"
)

var SupportedArgocdActions = []string{
	"sync",
	"rollback",
	"diff",
	"refresh",
}

type Client struct {
	httpClient *resty.Client
}

func NewClient(token, baseURL string) (*Client, error) {
	cookies, err := httputil.SetArgoCDRequestTokenCookie(token, true, true, "")
	if err != nil {
		return nil, fmt.Errorf("set ArgoCD request token cookie: %w", err)
	}

	return &Client{
		httpClient: resty.New().SetBaseURL(baseURL).SetCookies(cookies),
	}, nil
}

func validateResp(resp *resty.Response) error {
	if resp.IsError() {
		apiStatus := errors.NewAPIStatus(resp.StatusCode(), string(resp.Body()))
		return status.Error(apiStatus.GRPCStatus().Code(), apiStatus.Message)
	}
	return nil
}

func (c *Client) GetUserInfo(ctx context.Context) (*UserInfo, error) {
	info := UserInfo{}

	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&info).Get("/api/v1/session/userinfo")
	if err != nil {
		return nil, err
	}
	return &info, validateResp(resp)
}

func (c *Client) GetApplication(ctx context.Context, appName string, opts ...func([]byte) error) (*Application, error) {
	app := Application{}

	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&app).Get(fmt.Sprintf("/api/v1/applications/%s", appName))
	if err != nil {
		return nil, err
	}
	err = validateResp(resp)
	if err != nil {
		return nil, err
	}
	for _, opt := range opts {
		if err := opt(resp.Body()); err != nil {
			return nil, err
		}
	}
	return &app, err
}

func (c *Client) ListApplications(ctx context.Context, search string, fields []string) ([]Application, error) {
	appList := ApplicationList{}
	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&appList).Get(fmt.Sprintf("/api/v1/applications?search=%s&fields=%s", url.QueryEscape(strings.TrimSpace(search)), url.QueryEscape(strings.Join(fields, ","))))
	if err != nil {
		return nil, err
	}
	return appList.Items, validateResp(resp)
}

func (c *Client) GetAppTree(ctx context.Context, appName string) (*ApplicationTree, error) {
	var appTree ApplicationTree
	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&appTree).Get(fmt.Sprintf("/api/v1/applications/%s/resource-tree?appNamespace=argocd", appName))
	if err != nil {
		return nil, err
	}
	return &appTree, validateResp(resp)
}

func (c *Client) GetLogs(ctx context.Context, appName string, resource ResourceRef, container string, tailLines int64) ([]string, error) {
	resp, err := c.httpClient.R().SetContext(ctx).
		Get(fmt.Sprintf("/api/v1/applications/%s/logs?appNamespace=argocd&namespace=%s&follow=false&group=%s&kind=%s&resourceName=%s&tailLines=%d&container=%s",
			appName,
			resource.Namespace,
			resource.Group,
			resource.Kind,
			resource.Name,
			tailLines,
			container))
	if err != nil {
		return nil, err
	}

	if err := validateResp(resp); err != nil {
		return nil, err
	}

	var logs []string
	for _, partStr := range strings.Split(string(resp.Body()), "\n") {
		if partStr == "" {
			continue
		}
		var part map[string]interface{}
		if err := json.Unmarshal([]byte(partStr), &part); err != nil {
			return nil, err
		}
		if log, ok, err := unstructured.NestedString(part, "result", "content"); ok && err == nil {
			logs = append(logs, log)
		}
	}
	return logs, nil
}

func (c *Client) GetManagedResource(ctx context.Context, appName string, resource ResourceRef) (*ManagedResourcesResponse, error) {
	var managedResourcesResponse ManagedResourcesResponse
	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&managedResourcesResponse).Get(
		fmt.Sprintf("/api/v1/applications/%s/managed-resources?appNamespace=argocd&namespace=%s&group=%s&kind=%s&name=%s",
			appName,
			resource.Namespace,
			resource.Group,
			resource.Kind,
			resource.Name))
	if err != nil {
		return nil, err
	}
	return &managedResourcesResponse, validateResp(resp)
}

func (c *Client) GetResourceManifest(ctx context.Context, appName string, resource ResourceRef) (string, error) {
	var resourceResponse struct {
		Manifest string `json:"manifest"`
	}
	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&resourceResponse).Get(
		fmt.Sprintf("/api/v1/applications/%s/resources?appNamespace=argocd&namespace=%s&group=%s&kind=%s&name=%s",
			appName,
			resource.Namespace,
			resource.Group,
			resource.Kind,
			resource.Name))
	if err != nil {
		return "", err
	}
	return resourceResponse.Manifest, validateResp(resp)
}

func (c *Client) GetApplicationEvents(ctx context.Context, appName string) (*v1.EventList, error) {
	var events v1.EventList
	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&events).Get(
		fmt.Sprintf("/api/v1/applications/%s/events?appNamespace=argocd", appName))
	if err != nil {
		return nil, err
	}
	return &events, validateResp(resp)
}

func (c *Client) ListResourceActions(ctx context.Context, appName string, resource ResourceRef) ([]ResourceAction, error) {
	res := struct {
		Actions []ResourceAction
	}{}
	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&res).Get(
		fmt.Sprintf("/api/v1/applications/%s/resource/actions?appNamespace=argocd&namespace=%s&group=%s&kind=%s&resourceName=%s&version=",
			appName,
			resource.Namespace,
			resource.Group,
			resource.Kind,
			resource.Name))
	if err != nil {
		return nil, err
	}
	return res.Actions, validateResp(resp)
}

func (c *Client) RunAction(ctx context.Context, appName string, resource ResourceRef, action string) error {
	data, err := json.Marshal(action)
	if err != nil {
		return err
	}
	resp, err := c.httpClient.R().SetContext(ctx).SetBody(data).SetHeader("Content-Type", "application/json").Post(
		fmt.Sprintf("/api/v1/applications/%s/resource/actions?appNamespace=argocd&namespace=%s&group=%s&kind=%s&resourceName=%s&version=",
			appName,
			resource.Namespace,
			resource.Group,
			resource.Kind,
			resource.Name))
	if err != nil {
		return nil
	}
	return validateResp(resp)
}

func (c *Client) SyncApplication(ctx context.Context, appName string) (*Application, error) {
	app := Application{}

	currentApp, err := c.GetApplication(ctx, appName)
	if err != nil {
		return nil, err
	}
	body, err := json.Marshal(map[string]any{
		"appNamespace": currentApp.Namespace,
	})
	if err != nil {
		return nil, err
	}

	resp, err := c.httpClient.R().SetContext(ctx).SetBody(body).SetHeader("Content-Type", "application/json").Post(fmt.Sprintf("/api/v1/applications/%s/sync", appName))
	if err != nil {
		return nil, err
	}
	return &app, validateResp(resp)
}

func (c *Client) PatchApplication(ctx context.Context, appName string, patch map[string]interface{}) error {
	patchData, err := json.Marshal(patch)
	if err != nil {
		return err
	}
	body, err := json.Marshal(map[string]interface{}{
		"patchType": "merge",
		"patch":     string(patchData),
	})
	if err != nil {
		return err
	}
	resp, err := c.httpClient.R().SetContext(ctx).SetBody(body).SetHeader("Content-Type", "application/json").Patch(fmt.Sprintf("/api/v1/applications/%s", appName))
	if err != nil {
		return err
	}
	return validateResp(resp)
}

func (c *Client) RollbackApplication(ctx context.Context, appName string) (*Application, error) {
	app := Application{}

	currentApp, err := c.GetApplication(ctx, appName)
	if err != nil {
		return nil, err
	}
	if currentApp.Spec.SyncPolicy != nil {
		if err := c.PatchApplication(ctx, appName, map[string]interface{}{
			"spec": map[string]interface{}{
				"syncPolicy": nil,
			},
		}); err != nil {
			return nil, fmt.Errorf("failed to remove sync policy: %w", err)
		}
	}

	if len(currentApp.Status.History) < 2 {
		return nil, fmt.Errorf("no previous version found for application %s", appName)
	}

	body, err := json.Marshal(map[string]any{
		"appNamespace": currentApp.Namespace,
		"id":           currentApp.Status.History[len(currentApp.Status.History)-2].ID,
	})
	if err != nil {
		return nil, err
	}
	resp, err := c.httpClient.R().SetContext(ctx).SetBody(body).SetResult(&app).SetHeader("Content-Type", "application/json").Post(fmt.Sprintf("/api/v1/applications/%s/rollback", appName))
	if err != nil {
		return nil, err
	}
	return &app, validateResp(resp)
}

func (c *Client) DiffApplication(ctx context.Context, appName string) ([]string, error) {
	var diffResult struct {
		Items []ResourceDiff `json:"items"`
	}

	currentApp, err := c.GetApplication(ctx, appName)
	if err != nil {
		return nil, err
	}

	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&diffResult).Get(fmt.Sprintf("/api/v1/applications/%s/managed-resources?appNamespace=%s&fields=%s",
		appName,
		currentApp.Namespace,
		url.QueryEscape("items.normalizedLiveState,items.predictedLiveState,items.group,items.kind,items.namespace,items.name")))
	if err != nil {
		return nil, err
	}

	var left, right string
	for _, item := range diffResult.Items {
		if item.PredictedLiveState == "" {
			continue
		}
		var leftYAML, rightYAML map[string]any
		if err := yaml.Unmarshal([]byte(item.NormalizedLiveState), &leftYAML); err != nil {
			return nil, err
		}
		if err := yaml.Unmarshal([]byte(item.PredictedLiveState), &rightYAML); err != nil {
			return nil, err
		}

		cleanupK8sResource(leftYAML)
		cleanupK8sResource(rightYAML)

		var leftYAMLData []byte
		leftYAMLData, err = yaml.Marshal(leftYAML)
		if err != nil {
			return nil, err
		}
		var rightYAMLData []byte
		rightYAMLData, err = yaml.Marshal(rightYAML)
		if err != nil {
			return nil, err
		}
		left += fmt.Sprintf("---\n%s\n", leftYAMLData)
		right += fmt.Sprintf("---\n%s\n", rightYAMLData)
	}

	return []string{left, right}, validateResp(resp)
}

func cleanupK8sResource(resource map[string]any) {
	delete(resource, "status")

	if metadata, ok := resource["metadata"].(map[string]any); ok {
		delete(metadata, "managedFields")
		delete(metadata, "generation")
		delete(metadata, "resourceVersion")
		delete(metadata, "uid")
		delete(metadata, "creationTimestamp")

		if annotations, ok := metadata["annotations"].(map[string]any); ok {
			delete(annotations, "kubectl.kubernetes.io/last-applied-configuration")
			delete(annotations, "argocd.argoproj.io/sync-options")
			delete(annotations, "argocd.argoproj.io/refresh")

			if len(annotations) == 0 {
				delete(metadata, "annotations")
			}
		}
	}
}

func (c *Client) RefreshApplication(ctx context.Context, appName string) (*Application, error) {
	app := Application{}

	currentApp, err := c.GetApplication(ctx, appName)
	if err != nil {
		return nil, err
	}

	resp, err := c.httpClient.R().SetContext(ctx).SetResult(&app).Get(fmt.Sprintf("/api/v1/applications/%s?refresh=normal&appNamespace=%s", appName, currentApp.Namespace))
	if err != nil {
		return nil, err
	}
	return &app, validateResp(resp)
}
