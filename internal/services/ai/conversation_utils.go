package ai

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"time"

	jsonpatch "github.com/evanphx/json-patch"
	"github.com/samber/lo"
	"github.com/sasha<PERSON>nov/go-openai"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"k8s.io/apimachinery/pkg/util/validation"

	"github.com/akuityio/akuity-platform/internal/services/ai/clients"
	"github.com/akuityio/akuity-platform/internal/services/ai/functions"
	"github.com/akuityio/akuity-platform/internal/services/ai/reposet"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/models"
)

type aiConversationUtils struct {
	db      *sql.DB
	repoSet reposet.ResourceRepoSet

	akUsersById map[string]string

	// conversation holds the original conversation state.
	// don't modify it directly, instead modify separate fields and then call save() to persist changes.
	conversation *models.AiConversation

	title    null.String
	public   null.Bool
	metadata *models.AIConversationMetadata
	contexts []models.ConversationContext
	messages []*models.AIConversationMessage
	runbooks []string
	userID   string
}

// applyPatch calculates the different between beforeChange and afterChange and applies it to target.
func applyPatch(beforeChange, afterChange, current, target interface{}) (bool, error) {
	beforeChangeJson, err := json.Marshal(beforeChange)
	if err != nil {
		return false, err
	}
	afterChangeJson, err := json.Marshal(afterChange)
	if err != nil {
		return false, err
	}
	patch, err := jsonpatch.CreateMergePatch(beforeChangeJson, afterChangeJson)
	if err != nil {
		return false, err
	}
	if string(patch) == "{}" {
		return false, nil
	}
	currentJson, err := json.Marshal(current)
	if err != nil {
		return false, err
	}
	mergedJson, err := jsonpatch.MergePatch(currentJson, patch)
	if err != nil {
		return false, err
	}
	if err := json.Unmarshal(mergedJson, target); err != nil {
		return false, err
	}
	return true, nil
}

func newAiConversationUtils(ctx context.Context, repoSet reposet.ResourceRepoSet, db *sql.DB, conversation *models.AiConversation) (*aiConversationUtils, error) {
	util := &aiConversationUtils{
		db:           db,
		repoSet:      repoSet,
		conversation: conversation,
	}
	if err := util.init(ctx); err != nil {
		return nil, err
	}
	return util, nil
}

func (s *aiConversationUtils) init(ctx context.Context) error {
	s.title = s.conversation.Title
	s.public = s.conversation.Public
	s.userID = s.conversation.UserID

	var err error
	if s.metadata, err = s.conversation.GetMetadata(); err != nil {
		return err
	}

	if s.contexts, err = s.conversation.GetContexts(); err != nil {
		return err
	}

	if s.runbooks, err = s.conversation.GetRunbooks(); err != nil {
		return err
	}

	if s.messages, err = s.conversation.GetMessages(); err != nil {
		return err
	}
	s.messages = ensureToolCallsOrder(s.messages)
	if s.akUsersById, err = getAkUsersById(s.repoSet.GetRepoSet(), ctx, s.messages); err != nil {
		return err
	}
	return nil
}

func (s *aiConversationUtils) wrapContext(ctx context.Context) context.Context {
	ctx = functions.WithConversation(ctx, s.getConversation())
	ctx = functions.WithConversationMeta(ctx, s.getMetadata())
	ctx = clients.WithCallerInfo(ctx, clients.CallerInfo{OrganizationID: s.conversation.OrganizationID, ConversationMeta: s.getMetadata()})
	return ctx
}

func (s *aiConversationUtils) needProcessing() bool {
	_, found := lo.Find(s.messages, func(item *models.AIConversationMessage) bool {
		return !item.Processed
	})
	return found
}

func (s *aiConversationUtils) getTitle() null.String {
	return s.title
}

func (s *aiConversationUtils) setTitle(title string) {
	s.title = null.StringFrom(title)
}

func (s *aiConversationUtils) setUserID(userID string) {
	s.userID = userID
}

func (s *aiConversationUtils) setPublic(public bool) {
	s.public = null.BoolFrom(public)
}

func (s *aiConversationUtils) setContexts(contexts []models.ConversationContext) {
	s.contexts = contexts
}

func (s *aiConversationUtils) setRunbooks(runbooks []string) {
	s.runbooks = runbooks
}

func (s *aiConversationUtils) getMetadata() *models.AIConversationMetadata {
	return s.metadata
}

func (s *aiConversationUtils) getConversation() *models.AiConversation {
	return s.conversation
}

// updateConversationTasks removes tasks that are due to run from the conversation metadata
func (s *aiConversationUtils) updateConversationTasks(ctx context.Context) error {
	var needRun, other []models.AiTask
	for _, task := range s.metadata.Tasks {
		if time.Now().After(task.Timestamp) {
			needRun = append(needRun, task)
		} else {
			other = append(other, task)
		}
	}
	s.metadata.Tasks = other

	if len(needRun) > 0 {
		if err := s.appendChatCompletionMessage(ai.Message{
			Role: ai.MessageRoleDeveloper,
			Content: fmt.Sprintf("It is time to run tasks with description: %s", strings.Join(lo.Map(needRun, func(t models.AiTask, index int) string {
				return fmt.Sprintf("* %s", t.Description)
			}), "\n")),
		}); err != nil {
			return err
		}
	}

	if len(s.metadata.Tasks) > 0 {
		ctrl := database.GetControllerFromContext(ctx)
		if ctrl == nil {
			return errors.New("failed to get database controller from context")
		}
		sort.Slice(s.metadata.Tasks, func(i, j int) bool {
			return s.metadata.Tasks[i].Timestamp.Compare(s.metadata.Tasks[j].Timestamp) < 0
		})

		ctrl.Enqueue(s.conversation.ID, time.Until(s.metadata.Tasks[0].Timestamp))
	}

	return nil
}

func (s *aiConversationUtils) getMessages() []ai.Message {
	messages := lo.Map(s.messages, func(item *models.AIConversationMessage, index int) ai.Message {
		message := item.Message
		if item.Message.Role == ai.MessageRoleUser && item.User != nil {
			message.Content = fmt.Sprintf("user: %s:\ncontent:%s", item.GetUser(s.akUsersById), item.Message.Content)
		}
		return message
	})
	messages = lo.Filter(messages, func(item ai.Message, index int) bool {
		return item.Content != "" || item.Role != ""
	})
	return messages
}

func (s *aiConversationUtils) appendChatCompletionMessage(message ai.Message, opts ...func(msg *models.AIConversationMessage)) error {
	msg, err := newConversationMessage(message, opts...)
	if err != nil {
		return err
	}
	s.messages = append(s.messages, msg)
	return nil
}

func (s *aiConversationUtils) appendMessage(message *models.AIConversationMessage) {
	s.messages = append(s.messages, message)
}

// mergeMetadata finds the changes applied to metadata and applies them to the database conversation and sets it to current conversation.
func (s *aiConversationUtils) mergeMetadata(dbCnv *models.AiConversation) (bool, error) {
	current, err := dbCnv.GetMetadata()
	if err != nil {
		return false, err
	}
	s.metadata.LastProcessTime = time.Now()
	unchanged, err := s.conversation.GetMetadata()
	if err != nil {
		return false, err
	}
	var merged models.AIConversationMetadata
	updated, err := applyPatch(unchanged, s.metadata, current, &merged)
	if err != nil {
		return false, err
	}
	if err := dbCnv.SetMetadata(&merged); err != nil {
		return false, err
	}
	return updated, nil
}

// mergeMessages finds the changes applied to messages and applies them to the database conversation and sets it to current conversation.
func (s *aiConversationUtils) mergeMessages(dbCnv *models.AiConversation) (bool, error) {
	// Validate and filter out invalid suggested contexts before saving
	s.validateSuggestedContexts()

	unchanged, err := s.conversation.GetMessages()
	if err != nil {
		return false, err
	}
	unchangedMessages := lo.KeyBy(unchanged, func(item *models.AIConversationMessage) string {
		return item.ID
	})

	merged, err := dbCnv.GetMessages()
	if err != nil {
		return false, err
	}
	dbMessagesById := lo.KeyBy(merged, func(item *models.AIConversationMessage) string {
		return item.ID
	})
	updated := false
	for _, newMsg := range s.messages {
		oldMsg, oldFound := unchangedMessages[newMsg.ID]
		if !oldFound {
			updated = true
			dbMessagesById[newMsg.ID] = newMsg
			continue
		}
		dbMsg, dbFound := dbMessagesById[newMsg.ID]
		if !dbFound {
			continue
		}
		var mergedMsg models.AIConversationMessage
		val, err := applyPatch(oldMsg, newMsg, dbMsg, &mergedMsg)
		if err != nil {
			return false, err
		}
		if val {
			dbMessagesById[newMsg.ID] = &mergedMsg
			updated = true
		}
	}
	merged = lo.Values(dbMessagesById)
	sort.Slice(merged, func(i, j int) bool {
		return merged[i].CreationTimestamp.Before(merged[j].CreationTimestamp)
	})
	if err := dbCnv.SetMessages(merged); err != nil {
		return false, err
	}
	return updated, nil
}

func isValidResourceName(name string) bool {
	errs := validation.IsDNS1123Subdomain(name)
	return len(errs) == 0
}

// validateSuggestedContexts validates if the suggested contexts exist in the system
// It filters out contexts that don't exist in the system but doesn't return an error
func (s *aiConversationUtils) validateSuggestedContexts() {
	for _, msg := range s.messages {
		if msg.Message.Role != ai.MessageRoleAssistant {
			continue
		}

		var validContexts []models.ConversationContext
		for _, suggestedCtx := range msg.AIResponse.SuggestedContexts {
			if suggestedCtx.ArgoCDApp != nil {
				if !isValidResourceName(suggestedCtx.ArgoCDApp.Name) {
					continue
				}

				validContexts = append(validContexts, suggestedCtx)
			}
			if suggestedCtx.K8SNamespace != nil {
				if !isValidResourceName(suggestedCtx.K8SNamespace.Name) {
					continue
				}

				validContexts = append(validContexts, suggestedCtx)
			}
			if suggestedCtx.KargoProject != nil {
				if !isValidResourceName(suggestedCtx.KargoProject.Name) {
					continue
				}

				validContexts = append(validContexts, suggestedCtx)
			}
		}
		// Update the message with only valid contexts
		msg.AIResponse.SuggestedContexts = validContexts
	}
}

func (s *aiConversationUtils) save(ctx context.Context) error {
	tx, err := s.db.BeginTx(ctx, &sql.TxOptions{})
	if err != nil {
		return err
	}
	defer func() {
		_ = tx.Rollback()
	}()

	dbCnv, err := models.AiConversations(
		qm.Where("id = ?", s.conversation.ID),
		qm.For("UPDATE"),
	).One(ctx, tx)
	if err != nil {
		return err
	}
	var updateColumns []string
	if dbCnv.Title.String != s.title.String {
		updateColumns = append(updateColumns, models.AiConversationColumns.Title)
		dbCnv.Title = s.title
	}

	if dbCnv.Public.Bool != s.public.Bool {
		updateColumns = append(updateColumns, models.AiConversationColumns.Public)
		dbCnv.Public = s.public
	}

	if dbCnv.UserID != s.userID {
		updateColumns = append(updateColumns, models.AiConversationColumns.UserID)
		dbCnv.UserID = s.userID
	}

	if updated, err := s.mergeContexts(dbCnv); err == nil && updated {
		updateColumns = append(updateColumns, models.AiConversationColumns.Contexts)
	} else if err != nil {
		return err
	}

	if updated, err := s.mergeRunbooks(dbCnv); err == nil && updated {
		updateColumns = append(updateColumns, models.AiConversationColumns.Runbooks)
	} else if err != nil {
		return err
	}

	if updated, err := s.mergeMetadata(dbCnv); err == nil && updated {
		updateColumns = append(updateColumns, models.AiConversationColumns.Metadata)
	} else if err != nil {
		return err
	}

	if updated, err := s.mergeMessages(dbCnv); err == nil && updated {
		updateColumns = append(updateColumns, models.AiConversationColumns.Messages)
	} else if err != nil {
		return err
	}
	if len(updateColumns) > 0 {
		if _, err := dbCnv.Update(ctx, tx, boil.Whitelist(updateColumns...)); err != nil {
			return err
		}
	}

	if err := tx.Commit(); err != nil {
		return err
	}

	s.conversation = dbCnv
	return s.init(ctx)
}

func (s *aiConversationUtils) mergeContexts(dbConv *models.AiConversation) (bool, error) {
	contexts, err := dbConv.GetContexts()
	if err != nil {
		return false, err
	}

	if !reflect.DeepEqual(s.contexts, contexts) {
		ctxsJSON, err := json.Marshal(s.contexts)
		if err != nil {
			return false, err
		}
		item, err := newConversationMessage(ai.Message{
			Role:    openai.ChatMessageRoleDeveloper,
			Content: fmt.Sprintf("New contexts: %s", string(ctxsJSON)),
		})
		if err != nil {
			return false, err
		}
		s.appendMessage(item)
		if err := dbConv.SetContexts(s.contexts); err != nil {
			return false, err
		}

		return true, nil
	}
	return false, nil
}

func (s *aiConversationUtils) mergeRunbooks(dbConv *models.AiConversation) (bool, error) {
	runbooks, err := dbConv.GetRunbooks()
	if err != nil {
		return false, err
	}

	if !reflect.DeepEqual(s.runbooks, runbooks) {
		// Only create a message if we actually have runbooks to add
		if len(s.runbooks) > 0 {
			runbooksJSON, err := json.Marshal(s.runbooks)
			if err != nil {
				return false, err
			}
			item, err := newConversationMessage(ai.Message{
				Role:    openai.ChatMessageRoleDeveloper,
				Content: fmt.Sprintf("New runbooks: %s", string(runbooksJSON)),
			})
			if err != nil {
				return false, err
			}
			s.appendMessage(item)
		}

		if err := dbConv.SetRunbooks(s.runbooks); err != nil {
			return false, err
		}

		return true, nil
	}
	return false, nil
}

func (s *aiConversationUtils) markMessagesAsProcessed() {
	for _, msg := range s.messages {
		msg.Processed = true
	}
}

func (s *aiConversationUtils) getUnprocessedToolCalls() []ai.ToolCall {
	toolCalls := map[string]ai.ToolCall{}
	for _, msg := range s.messages {
		switch msg.Message.Role {
		case ai.MessageRoleAssistant:
			for _, toolCall := range msg.Message.ToolCalls {
				toolCalls[toolCall.ID] = toolCall
			}
		case ai.MessageRoleTool:
			delete(toolCalls, msg.Message.ToolCallID)
		}
	}
	return lo.Values(toolCalls)
}

func subSlice[T any](arr []T, i, j int) []T {
	copied := make([]T, len(arr))
	copy(copied, arr)
	return copied[i:j]
}

// ensureToolCallsOrder sorts messages ensuring that all tool call results are next to their original tool call
func ensureToolCallsOrder(messages []*models.AIConversationMessage) []*models.AIConversationMessage {
	var toolCallResults []int
	for i := len(messages) - 1; i >= 0; i-- {
		msg := messages[i]
		if msg.Message.ToolCallID != "" {
			toolCallResults = append(toolCallResults, i)
		} else if len(msg.Message.ToolCalls) > 0 {
			for _, j := range toolCallResults {
				callMessage := messages[j]
				before := subSlice(messages, 0, i+1)
				between := subSlice(messages, i+1, j)
				after := subSlice(messages, j+1, len(messages))
				messages = append(append(append(before, callMessage), between...), after...)
				toolCallResults = nil
			}
		}
	}
	return messages
}
