package functions

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/samber/lo"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"

	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func isArgoCDContext(_ context.Context, conversationContext models.ConversationContext) bool {
	return conversationContext.ArgoCDApp != nil
}

func (fc *Controller) addArgoCDAppFunctions() error {
	for _, action := range []string{"sync", "rollback", "refresh"} {
		if err := addFunction(fc, Function{
			Tool: ai.Tool{
				Name:        fmt.Sprintf("argocd-run-app-%s-action", action),
				Description: fmt.Sprintf(`The function %ss the application.`, action),
			},
			DisplayName:           fmt.Sprintf("Run %s action on application", action),
			CheckContextSupported: isArgoCDContext,
			PromptSuggestion: &organizationv1.AIConversationSuggestion{
				Description: fmt.Sprintf("%s application", cases.Title(language.English).String(action)),
				Prompt:      fmt.Sprintf("Run the %s action on application", action),
			},
		}, func(ctx context.Context, args struct {
			ArgoCDApp models.ArgoCDApplicationConversationContext `json:"argoCDApp"`
		},
		) (string, error) {
			return fc.runAppAction(ctx, args.ArgoCDApp, action)
		}); err != nil {
			return err
		}
	}
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name: "argocd-list-applications",
			Description: `
			Retrieves a list of all Argo CD applications. 
			You can use this function to get the list of applications and then use the argocd-get-app function to get the details of a specific application.
			You may use the search parameter to filter the applications by name, optional, do not use regex like *
			`,
		},
		DisplayName:           "List Argo CD applications",
		CheckContextSupported: isArgoCDContext,
	}, fc.getArgoCDApplications); err != nil {
		return err
	}
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name: "argocd-get-app",
			Description: `
			Retrieves JSON marshalled Argo CD application resource. The manifest includes application spec as well application status.
			Below are the format of most important resource fields:
			- metadata: application name, namespace, labels and annotations
			- spec:
			- project: The project the application belongs to.
			- source: Source of the application manifests
				- repoURL: Can point to either a Helm chart repo or a git repo.
				- targetRevision: Git tag, branch, revision of hor Helm chart version.
				- path: Git directory (Git based apps only)
				- chart: Helm chart name (Helm based apps only)
			- sources: list of "source" with the same structure. App can have either "source" or "sources" but not both
			- destination: Destination cluster and namespace to deploy the application
				- server: Cluster k8s API server URL
				- name: Name of k8s cluster
				- namespace: Optional destination namespace
			- operation: Currently running operation
				- sync: Synchronization operation details which includes revision and various sync options
				- initiatedBy: Information about sync operation initiator
			- status:
				- resources: Array of Kubernetes resources managed by give Argo CD application:
				  - name: Name of the Kubernetes resource
				  - namespace: Kubernetes namespace where the resource exists
				  - group: API group (e.g., "apps", "networking.k8s.io", "" for core, etc.)
				  - version: API version of the resource
				  - kind: Resource type (e.g., Deployment, Service, ConfigMap, etc.)
				  - status: Sync status of the resource: OutOfSync, Synced or Unknown if status cannot be determined             
				  - health: Optional health status of the resource
					- status: Health status
					- message: Optional message describing status reason
				- sync: Aggregated application sync status that explains if application is synchronized with Git or not
					- status: Sync status of the resource: OutOfSync, Synced or Unknown if status cannot be determined
					- operationState: Information about most recent sync operation
					- history: Information about sync operations
					- comparedTo: Information about what application was compared to
						- source: the application source at the moment of comparison
						- destination: the application destination at the moment of comparison
					- revision: Resolved Git or Helm revision at the moment of comparison`,
		},
		DisplayName:           "Get Argo CD application",
		CheckContextSupported: isArgoCDContext,
	}, fc.getArgoCDApp); err != nil {
		return err
	}
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "argocd-get-managed-resource-diff",
			Description: `Returns the difference between the current state and the desired state of a resource managed by an Argo CD application.`,
		},
		DisplayName: "Get Argo CD managed resource diff",
	}, fc.getArgoCDManagedResourceDiff); err != nil {
		return err
	}
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "argocd-get-app-events",
			Description: `Returns the Argo CD K8S Events and Akuity Timeline Events.`,
		},
		DisplayName: "Get Argo CD application events",
		PromptSuggestion: &organizationv1.AIConversationSuggestion{
			Description: "Inspect K8S and Akuity Platform timeline events",
			Prompt:      "Show me recent application events",
		},
		CheckContextSupported: isArgoCDContext,
	}, fc.getArgoCDApplicationEvents); err != nil {
		return err
	}

	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name: "argocd-get-app-tree",
			Description: `
			Returns the JSON serialized Argo CD resource tree which includes both resources managed by Argo CD
			and all child resources produced by managed resources.
			
			## Filter Usage Strategy
			
			**CRITICAL**: Always use filters to avoid unnecessary token usage and focus on relevant data.
			Parameters:
			- resourceIDs: Optional list of resource IDs to filter the tree. If provided, only the specified resources and their children will be returned.
			- healthStatus: Optional filter to only return resources with specific health status (["Degraded"], ["Progressing"], ["Healthy"], ["Missing"], ["Unknown"], or combinations). Use ["Degraded", "Missing", "Unknown", "Progressing"] for troubleshooting. If specified, returns complete ownership chains.
			- kind: Filter resources by kind (e.g., ["Pod", "Deployment", "Service"]). Case-sensitive. Use for specific resource types.
			- name: Filter resources by name pattern (contains match, case-insensitive). Useful for finding resources with specific names.
			
			The response format is following:
			- nodes: kubernetes resources with information about relationship between them
			  - version: API version of the resource
			  - group: API group (e.g., "apps", "networking.k8s.io", "" for core, etc.)
			  - kind: Resource type (e.g., Deployment, Service, ConfigMap, etc.)
			  - name: Name of the Kubernetes resource
			  - namespace: Kubernetes namespace where the resource exists
			  - uid: Unique resource identifier
			  - parentRefs: List of resources that own current resource. The managed resources are owned by application and don't have parentRefs
			  - info: list of name, value pairs with additional metadata e.g. [{"name": "Containers","value": "1/1"}]
			  - health: Optional health status of the resource
				- status: Health status
				- message: Optional message describing status reason`,
		},
		DisplayName: "Get Argo CD application tree",
	}, fc.getArgoCDAppTree); err != nil {
		return err
	}

	return nil
}

func (fc *Controller) getArgoCDApplications(ctx context.Context, args struct {
	InstanceID string `json:"instanceID"`
	Search     string `json:"search" description:"Search applications by name, optional, do not use regex like *"`
},
) (string, error) {
	argocdClient := fc.ArgoCDClientSet.GetArgoCDClient(args.InstanceID)
	if argocdClient == nil {
		return "Failed to get ArgoCD client", nil
	}
	apps, err := argocdClient.ListApplications(ctx, args.Search, []string{})
	if err != nil {
		return "", err
	}
	data, err := json.Marshal(apps)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// runAction runs the specified action on the specified resource
func (fc *Controller) runAppAction(ctx context.Context, app models.ArgoCDApplicationConversationContext, action string) (string, error) {
	argocdClient := fc.ArgoCDClientSet.GetArgoCDClient(app.InstanceID)
	if argocdClient == nil {
		return "Failed to get ArgoCD client", nil
	}
	switch action {
	case "sync":
		if _, err := argocdClient.SyncApplication(ctx, app.Name); err != nil {
			return "", err
		}
		return fmt.Sprintf("Application %s was successfully synced", app.Name), nil
	case "rollback":
		if _, err := argocdClient.RollbackApplication(ctx, app.Name); err != nil {
			return "", err
		}
		return fmt.Sprintf("Application %s was successfully rolled back", app.Name), nil
	case "refresh":
		if _, err := argocdClient.RefreshApplication(ctx, app.Name); err != nil {
			return "", err
		}
		return fmt.Sprintf("Application %s was successfully refreshed", app.Name), nil
	default:
		return "Operation not supported", nil
	}
}

type ResourceFilter struct {
	ResourceIDs  []string `json:"resourceIDs,omitempty" description:"Optional list of resource IDs to filter the tree. If provided, only the specified resources and their children will be returned."`
	HealthStatus []string `json:"healthStatus,omitempty" description:"Filter resources by health status: Degraded, Progressing, or Healthy. Can specify multiple statuses."`
	Kind         []string `json:"kind,omitempty" description:"Filter resources by kind (e.g., Pod, Deployment, Service, ConfigMap). Case-sensitive."`
	Name         string   `json:"name,omitempty" description:"Filter resources by name pattern (contains match, case-insensitive)"`
}

type Resource interface {
	GetID() string
	GetParentIDs() []string
	GetHealthStatus() string
	GetKind() string
	GetName() string
}

type ArgoCDResource struct {
	Node argocd.ResourceNode
}

func (a ArgoCDResource) GetID() string {
	return a.Node.UID
}

func (a ArgoCDResource) GetParentIDs() []string {
	return lo.Map(a.Node.ParentRefs, func(ref argocd.ResourceRef, _ int) string {
		return ref.UID
	})
}

func (a ArgoCDResource) GetHealthStatus() string {
	if a.Node.Health != nil {
		return string(a.Node.Health.Status)
	}
	return ""
}

func (a ArgoCDResource) GetKind() string {
	return a.Node.Kind
}

func (a ArgoCDResource) GetName() string {
	return a.Node.Name
}

type K8SObject struct {
	Object *models.ArgoCDClusterK8SObject
}

func (k K8SObject) GetID() string {
	if k.Object == nil {
		return ""
	}
	return k.Object.ID
}

func (k K8SObject) GetParentIDs() []string {
	if k.Object == nil {
		return []string{}
	}
	if k.Object.OwnerID.String != "" {
		return []string{k.Object.OwnerID.String}
	}
	return []string{}
}

func (k K8SObject) GetHealthStatus() string {
	if k.Object == nil {
		return ""
	}
	healthInfo, err := k.Object.GetArgoCDApplicationInfo()
	if err != nil {
		return ""
	}
	return healthInfo.GetHealthStatus()
}

func (k K8SObject) GetKind() string {
	if k.Object == nil {
		return ""
	}
	return k.Object.Kind.String
}

func (k K8SObject) GetName() string {
	if k.Object == nil {
		return ""
	}
	return k.Object.Name
}

func filterResourcesWithConditions(
	resources []Resource,
	resourceFilter ResourceFilter,
) []Resource {
	if len(resourceFilter.ResourceIDs) == 0 && len(resourceFilter.HealthStatus) == 0 && len(resourceFilter.Kind) == 0 && resourceFilter.Name == "" {
		return resources
	}

	resourceMap := lo.KeyBy(resources, func(resource Resource) string {
		return resource.GetID()
	})

	childrenMap := make(map[string][]Resource)
	for _, resource := range resources {
		for _, parentID := range resource.GetParentIDs() {
			childrenMap[parentID] = append(childrenMap[parentID], resource)
		}
	}

	var matchingResources []Resource
	for _, resource := range resources {
		matched := false
		if len(resourceFilter.ResourceIDs) > 0 && slices.Contains(resourceFilter.ResourceIDs, resource.GetID()) {
			matched = true
		}
		if len(resourceFilter.HealthStatus) > 0 && (slices.Contains(resourceFilter.HealthStatus, resource.GetHealthStatus()) ||
			(slices.Contains(resourceFilter.HealthStatus, "Healthy") && resource.GetHealthStatus() == "")) {
			matched = true
		}

		if len(resourceFilter.Kind) > 0 && slices.Contains(resourceFilter.Kind, resource.GetKind()) {
			matched = true
		}
		if resourceFilter.Name != "" && strings.Contains(strings.ToLower(resource.GetName()), strings.ToLower(resourceFilter.Name)) {
			matched = true
		}

		if matched {
			matchingResources = append(matchingResources, resource)
		}
	}

	resourcesInChain := make(map[string]Resource)
	for _, resource := range matchingResources {
		resourcesInChain[resource.GetID()] = resource
		// add parents and children
		current := resource
		for len(current.GetParentIDs()) > 0 {
			foundParent := false
			for _, parentID := range current.GetParentIDs() {
				if parent, exists := resourceMap[parentID]; exists {
					resourcesInChain[parent.GetID()] = parent
					current = parent
					foundParent = true
					break
				}
			}
			if !foundParent {
				break
			}
		}
		queue := []Resource{resource}
		for len(queue) > 0 {
			current := queue[0]
			queue = queue[1:]

			for _, child := range childrenMap[current.GetID()] {
				if _, exists := resourcesInChain[child.GetID()]; !exists {
					resourcesInChain[child.GetID()] = child
					queue = append(queue, child)
				}
			}
		}
	}
	return lo.Values(resourcesInChain)
}

func (fc *Controller) getArgoCDAppTree(ctx context.Context, args struct {
	ArgoCDApp      models.ArgoCDApplicationConversationContext `json:"argoCDApp"  required:"true"`
	ResourceFilter ResourceFilter                              `json:"resourceFilter,omitempty"`
},
) (string, error) {
	ac := fc.ArgoCDClientSet.GetArgoCDClient(args.ArgoCDApp.InstanceID)
	if ac == nil {
		return "", fmt.Errorf("ArgoCD client not found")
	}

	app, err := ac.GetApplication(ctx, args.ArgoCDApp.Name)
	if err != nil {
		return "", err
	}

	tree, err := ac.GetAppTree(ctx, args.ArgoCDApp.Name)
	if err != nil {
		return "", err
	}

	allNodes := append(tree.Nodes, tree.OrphanedNodes...)
	allNodes = fc.mergeApplicationStatusWithTreeNodes(allNodes, app.Status.Resources)
	resources := lo.Map(allNodes, func(node argocd.ResourceNode, _ int) Resource {
		return ArgoCDResource{Node: node}
	})
	filteredResources := filterResourcesWithConditions(resources, args.ResourceFilter)

	nodeUIDsToKeep := make(map[string]argocd.ResourceNode)
	for _, resource := range filteredResources {
		nodeUIDsToKeep[resource.GetID()] = resource.(ArgoCDResource).Node
	}

	tree.Nodes = lo.FilterMap(tree.Nodes, func(node argocd.ResourceNode, _ int) (argocd.ResourceNode, bool) {
		if updatedNode, exists := nodeUIDsToKeep[node.UID]; exists {
			return updatedNode, true
		}
		return node, true
	})

	tree.OrphanedNodes = lo.FilterMap(tree.OrphanedNodes, func(node argocd.ResourceNode, _ int) (argocd.ResourceNode, bool) {
		if updatedNode, exists := nodeUIDsToKeep[node.UID]; exists {
			return updatedNode, true
		}
		return node, true
	})
	data, err := json.Marshal(tree)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

func (fc *Controller) mergeApplicationStatusWithTreeNodes(nodes []argocd.ResourceNode, appResources []argocd.ResourceStatus) []argocd.ResourceNode {
	appResourceMap := make(map[string]argocd.ResourceStatus)
	for _, res := range appResources {
		key := fmt.Sprintf("%s/%s/%s/%s/%s", res.Group, res.Version, res.Kind, res.Namespace, res.Name)
		appResourceMap[key] = res
	}

	results := make([]argocd.ResourceNode, len(nodes))
	for i, node := range nodes {
		results[i] = node

		key := fmt.Sprintf("%s/%s/%s/%s/%s", node.Group, node.Version, node.Kind, node.Namespace, node.Name)
		if appRes, exists := appResourceMap[key]; exists {
			if results[i].Health == nil && appRes.Health != nil {
				results[i].Health = appRes.Health
			}

			syncInfo := argocd.InfoItem{
				Name:  "Sync Status",
				Value: string(appRes.Status),
			}

			hasSync := false
			for _, info := range results[i].Info {
				if info.Name == "Sync Status" {
					hasSync = true
					break
				}
			}

			if !hasSync {
				results[i].Info = append(results[i].Info, syncInfo)
			}
		}
	}
	return results
}

func (fc *Controller) getArgoCDApp(ctx context.Context, args struct {
	ArgoCDApp models.ArgoCDApplicationConversationContext `json:"argoCDApp"`
},
) (string, error) {
	ac := fc.ArgoCDClientSet.GetArgoCDClient(args.ArgoCDApp.InstanceID)
	if ac == nil {
		return "", fmt.Errorf("ArgoCD client not found")
	}
	app := map[string]interface{}{}
	_, err := ac.GetApplication(ctx, args.ArgoCDApp.Name, func(data []byte) error {
		return json.Unmarshal(data, &app)
	})
	if err != nil {
		if ok, err := checkArgoCDAppError(args.ArgoCDApp.Name, err); ok {
			return "", err
		}
		return "", err
	}

	unstructured.RemoveNestedField(app, "metadata", "managedFields")
	appJSON, err := json.Marshal(app)
	return string(appJSON), err
}

func (fc *Controller) getArgoCDManagedResourceDiff(ctx context.Context, args struct {
	ResourceID ResourceID                                  `json:"resourceID"`
	ArgoCDApp  models.ArgoCDApplicationConversationContext `json:"argoCDApp"`
},
) (string, error) {
	argocdClient := fc.ArgoCDClientSet.GetArgoCDClient(args.ArgoCDApp.InstanceID)
	if argocdClient == nil {
		return "Failed to get ArgoCD client", nil
	}
	resourceRef := argocd.ResourceRef{
		Group:     args.ResourceID.Group,
		Version:   args.ResourceID.Version,
		Kind:      args.ResourceID.Kind,
		Namespace: args.ResourceID.Namespace,
		Name:      args.ResourceID.Name,
	}

	diff, err := argocdClient.GetResourceDiff(ctx, args.ArgoCDApp.Name, resourceRef)
	if err != nil {
		if ok, err := checkArgoCDAppError(args.ArgoCDApp.Name, err); ok {
			return "", err
		}
		return "", err
	}
	return diff, nil
}

func (fc *Controller) getArgoCDApplicationEvents(ctx context.Context, args struct {
	ArgoCDApp models.ArgoCDApplicationConversationContext `json:"argoCDApp"`
},
) (string, error) {
	argocdClient := fc.ArgoCDClientSet.GetArgoCDClient(args.ArgoCDApp.InstanceID)
	if argocdClient == nil {
		return "Failed to get ArgoCD client", nil
	}
	k8sEventsResp, err := argocdClient.GetApplicationEvents(ctx, args.ArgoCDApp.Name)
	if err != nil {
		if ok, err := checkArgoCDAppError(args.ArgoCDApp.Name, err); ok {
			return "", err
		}
		return "", err
	}
	var resp []string
	if len(k8sEventsResp.Items) == 0 {
		resp = append(resp, "No k8s events found")
	} else {
		resp = append(resp, fmt.Sprintf("Found following k8s events:\n%s", strings.Join(lo.Map(k8sEventsResp.Items, func(item v1.Event, index int) string {
			return fmt.Sprintf("time: %s, type: %s, reason: %s, message: %s", item.LastTimestamp.UTC().String(), item.Type, item.Reason, item.Message)
		}), "\n")))
	}

	clusterID, err := fc.getAppClusterId(ctx, args.ArgoCDApp)
	if err != nil {
		return "", err
	}

	events, err := fc.ResSvc.ListKubernetesTimelineEvents(ctx, args.ArgoCDApp.InstanceID, []string{clusterID},
		nil, []string{args.ArgoCDApp.Name}, nil, time.Now().Add(-time.Hour*6), time.Now())
	if err != nil {
		return "", err
	}

	if len(events) == 0 {
		resp = append(resp, "No timeline events found")
	} else {
		resp = append(resp, fmt.Sprintf("Found following timeline events:\n%s", strings.Join(lo.Map(events, func(event *organizationv1.TimelineEvent, index int) string {
			return fmt.Sprintf("time: %s, event: %s", event.Timestamp.AsTime().UTC().String(), event.String())
		}), "\n")))
	}

	return strings.Join(resp, "\n"), nil
}
